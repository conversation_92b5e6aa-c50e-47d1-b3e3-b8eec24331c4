
import tkinter as tk
from tkinter import messagebox
import sys
import os

try:
    import pystray
    from PIL import Image
    TRAY_OK = True
except ImportError as e:
    TRAY_OK = False
    print(f"托盘导入失败: {e}")

def test_tray():
    if not TRAY_OK:
        messagebox.showerror("错误", "托盘功能不可用")
        return
    
    try:
        # 创建简单图标
        image = Image.new('RGB', (64, 64), 'blue')
        menu = pystray.Menu(pystray.MenuItem("测试", lambda: None))
        icon = pystray.Icon("测试", image, "测试托盘", menu)
        messagebox.showinfo("成功", "托盘功能正常！")
    except Exception as e:
        messagebox.showerror("错误", f"托盘测试失败: {e}")

root = tk.Tk()
root.title("最小化托盘测试")
root.geometry("300x200")

tk.Label(root, text="最小化托盘测试", font=("Arial", 14)).pack(pady=20)
tk.Label(root, text=f"托盘状态: {'✅ 可用' if TRAY_OK else '❌ 不可用'}").pack(pady=10)
tk.Button(root, text="测试托盘功能", command=test_tray).pack(pady=10)
tk.Button(root, text="退出", command=root.quit).pack(pady=10)

root.mainloop()
