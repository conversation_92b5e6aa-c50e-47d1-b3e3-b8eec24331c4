#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能验证脚本
批量图片自动转短视频 v3.5
项老师AI工作室出品
"""

import os
import sys

def verify_tray_functionality():
    """验证托盘功能"""
    print("🔍 验证系统托盘功能...")
    
    try:
        import pystray
        from PIL import Image
        import threading
        
        print("✅ pystray模块导入成功")
        
        # 测试创建简单托盘图标
        image = Image.new('RGB', (64, 64), color='blue')
        menu = pystray.Menu(
            pystray.MenuItem("测试", lambda: None)
        )
        
        icon = pystray.Icon("测试", image, "测试托盘", menu)
        print("✅ 托盘图标创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 托盘功能验证失败: {e}")
        return False

def verify_video_processing():
    """验证视频处理功能"""
    print("🔍 验证视频处理功能...")
    
    try:
        import cv2
        import numpy as np
        from PIL import Image
        
        print(f"✅ OpenCV版本: {cv2.__version__}")
        
        # 测试创建简单图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[:] = (255, 0, 0)  # 蓝色
        
        # 测试PIL处理
        pil_image = Image.fromarray(test_image)
        print("✅ PIL图像处理正常")
        
        # 测试视频编码器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        print("✅ 视频编码器可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 视频处理功能验证失败: {e}")
        return False

def verify_gui_components():
    """验证GUI组件"""
    print("🔍 验证GUI组件...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # 测试创建窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试组件
        frame = ttk.Frame(root)
        button = ttk.Button(frame, text="测试")
        label = ttk.Label(frame, text="测试")
        
        print("✅ Tkinter GUI组件正常")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI组件验证失败: {e}")
        return False

def verify_file_resources():
    """验证文件资源"""
    print("🔍 验证文件资源...")
    
    required_files = [
        "main.py",
        "gui_interface.py", 
        "video_processor.py",
        "logo.ico",
        "logo.png"
    ]
    
    all_exist = True
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"✅ {file_name} 存在")
        else:
            print(f"❌ {file_name} 缺失")
            all_exist = False
    
    return all_exist

def verify_executable():
    """验证可执行文件"""
    print("🔍 验证可执行文件...")
    
    exe_files = [
        "dist/批量图片自动转短视频_v3.5_完整版.exe"
    ]
    
    for exe_file in exe_files:
        if os.path.exists(exe_file):
            size = os.path.getsize(exe_file) / (1024 * 1024)  # MB
            print(f"✅ {exe_file} 存在 ({size:.1f} MB)")
        else:
            print(f"❌ {exe_file} 不存在")
            return False
    
    return True

def main():
    """主验证函数"""
    print("=" * 60)
    print("🎬 批量图片自动转短视频 - 功能验证")
    print("📱 项老师AI工作室出品 v3.5")
    print("=" * 60)
    
    results = []
    
    # 验证各项功能
    results.append(("文件资源", verify_file_resources()))
    results.append(("GUI组件", verify_gui_components()))
    results.append(("视频处理", verify_video_processing()))
    results.append(("系统托盘", verify_tray_functionality()))
    results.append(("可执行文件", verify_executable()))
    
    print("\n" + "=" * 60)
    print("📊 验证结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:12} : {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 所有功能验证通过！")
        print("🚀 程序已准备就绪，可以正常使用")
        print("\n📋 功能特性:")
        print("• ✅ 默认帧率60FPS")
        print("• ✅ 智能最小化到系统托盘")
        print("• ✅ 项老师logo图标")
        print("• ✅ 右键托盘菜单")
        print("• ✅ 后台运行支持")
        print("• ✅ 6重线性变化效果")
        print("• ✅ 15种专业动态效果")
        print("• ✅ 批量处理功能")
    else:
        print("❌ 部分功能验证失败，请检查环境配置")
    
    print("=" * 60)
    input("\n按回车键退出...")
    return all_passed

if __name__ == "__main__":
    main()
