#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可执行文件问题测试
专门测试为什么可执行文件中托盘功能不工作
"""

import os
import sys
import subprocess
import tempfile

def test_source_code():
    """测试源代码是否正常工作"""
    print("🔍 测试源代码...")
    
    try:
        # 直接运行main.py测试
        result = subprocess.run([sys.executable, "main.py"], 
                              capture_output=True, text=True, timeout=5)
        
        if "程序启动成功" in result.stdout:
            print("✅ 源代码启动成功")
            return True
        else:
            print("❌ 源代码启动失败")
            print("输出:", result.stdout)
            print("错误:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("✅ 源代码启动成功（超时但正常）")
        return True
    except Exception as e:
        print(f"❌ 源代码测试失败: {e}")
        return False

def test_executable():
    """测试可执行文件"""
    print("\n🔍 测试可执行文件...")
    
    desktop = os.path.expanduser("~/Desktop")
    exe_path = None
    
    # 查找可执行文件
    for file in os.listdir(desktop):
        if file.endswith('.exe') and '批量图片' in file:
            exe_path = os.path.join(desktop, file)
            break
    
    if not exe_path:
        print("❌ 没有找到可执行文件")
        return False
    
    print(f"📁 找到可执行文件: {os.path.basename(exe_path)}")
    
    try:
        # 尝试运行可执行文件
        result = subprocess.run([exe_path], 
                              capture_output=True, text=True, timeout=10)
        
        print("返回码:", result.returncode)
        if result.stdout:
            print("输出:", result.stdout[:500])
        if result.stderr:
            print("错误:", result.stderr[:500])
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("✅ 可执行文件启动成功（超时但正常）")
        return True
    except Exception as e:
        print(f"❌ 可执行文件测试失败: {e}")
        return False

def check_pyinstaller_spec():
    """检查PyInstaller spec文件"""
    print("\n🔍 检查PyInstaller配置...")
    
    if os.path.exists("build_final.spec"):
        with open("build_final.spec", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键配置
        checks = [
            ("pystray", "pystray依赖"),
            ("logo.ico", "图标文件"),
            ("tray_manager.py", "托盘管理器"),
            ("hiddenimports", "隐藏导入")
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description} 已配置")
            else:
                print(f"❌ {description} 缺失")
        
        return True
    else:
        print("❌ build_final.spec 文件不存在")
        return False

def analyze_build_log():
    """分析构建日志"""
    print("\n🔍 分析构建日志...")
    
    log_files = ["build/build_final/warn-build_final.txt"]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"📄 检查 {log_file}")
            with open(log_file, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 查找警告和错误
            lines = content.split('\n')
            warnings = [line for line in lines if 'WARNING' in line or 'ERROR' in line]
            
            if warnings:
                print("⚠️ 发现警告/错误:")
                for warning in warnings[:5]:  # 只显示前5个
                    print(f"   {warning}")
            else:
                print("✅ 没有发现严重警告")
        else:
            print(f"❌ {log_file} 不存在")

def create_minimal_test():
    """创建最小化测试程序"""
    print("\n🔍 创建最小化测试...")
    
    minimal_code = '''
import tkinter as tk
from tkinter import messagebox
import sys
import os

try:
    import pystray
    from PIL import Image
    TRAY_OK = True
except ImportError as e:
    TRAY_OK = False
    print(f"托盘导入失败: {e}")

def test_tray():
    if not TRAY_OK:
        messagebox.showerror("错误", "托盘功能不可用")
        return
    
    try:
        # 创建简单图标
        image = Image.new('RGB', (64, 64), 'blue')
        menu = pystray.Menu(pystray.MenuItem("测试", lambda: None))
        icon = pystray.Icon("测试", image, "测试托盘", menu)
        messagebox.showinfo("成功", "托盘功能正常！")
    except Exception as e:
        messagebox.showerror("错误", f"托盘测试失败: {e}")

root = tk.Tk()
root.title("最小化托盘测试")
root.geometry("300x200")

tk.Label(root, text="最小化托盘测试", font=("Arial", 14)).pack(pady=20)
tk.Label(root, text=f"托盘状态: {'✅ 可用' if TRAY_OK else '❌ 不可用'}").pack(pady=10)
tk.Button(root, text="测试托盘功能", command=test_tray).pack(pady=10)
tk.Button(root, text="退出", command=root.quit).pack(pady=10)

root.mainloop()
'''
    
    with open("minimal_tray_test.py", "w", encoding="utf-8") as f:
        f.write(minimal_code)
    
    print("✅ 最小化测试程序已创建: minimal_tray_test.py")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 可执行文件问题诊断")
    print("=" * 60)
    
    # 执行所有测试
    source_ok = test_source_code()
    exe_ok = test_executable()
    spec_ok = check_pyinstaller_spec()
    analyze_build_log()
    create_minimal_test()
    
    print("\n" + "=" * 60)
    print("📊 问题分析结果:")
    print("=" * 60)
    
    print(f"源代码运行: {'✅ 正常' if source_ok else '❌ 异常'}")
    print(f"可执行文件: {'✅ 正常' if exe_ok else '❌ 异常'}")
    print(f"构建配置: {'✅ 正常' if spec_ok else '❌ 异常'}")
    
    if source_ok and not exe_ok:
        print("\n🎯 问题确认: 源代码正常，但可执行文件有问题")
        print("\n🔧 可能原因:")
        print("1. PyInstaller打包时pystray依赖缺失")
        print("2. 图标文件在可执行文件中路径错误")
        print("3. Windows系统托盘权限问题")
        print("4. 可执行文件中缺少必要的DLL文件")
        
        print("\n💡 建议解决方案:")
        print("1. 重新安装pystray: pip uninstall pystray && pip install pystray")
        print("2. 使用--add-data明确包含所有资源文件")
        print("3. 使用--hidden-import明确包含pystray._win32")
        print("4. 测试minimal_tray_test.py确认环境")
    
    print("\n" + "=" * 60)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
