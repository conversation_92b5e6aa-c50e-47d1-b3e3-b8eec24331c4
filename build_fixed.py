#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版构建脚本 - 解决图标和托盘问题
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_dependencies():
    """检查所有依赖"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'pystray',
        'PIL',
        'cv2',
        'numpy',
        'tkinter',
        'pywin32'
    ]
    
    all_ok = True
    for package in required_packages:
        try:
            if package == 'PIL':
                from PIL import Image
            elif package == 'cv2':
                import cv2
            elif package == 'tkinter':
                import tkinter
            elif package == 'pywin32':
                import win32gui
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError as e:
            print(f"❌ {package}: {e}")
            all_ok = False
    
    return all_ok

def check_files():
    """检查必要文件"""
    print("🔍 检查必要文件...")
    
    required_files = [
        'main.py',
        'gui_interface.py',
        'video_processor.py',
        'tray_manager.py',
        'resource_utils.py',
        'build_final.spec'
    ]
    
    logo_files = ['logo.ico', 'logo.png', 'logo_correct.ico']
    
    all_ok = True
    
    # 检查Python文件
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 文件不存在")
            all_ok = False
    
    # 检查logo文件
    logo_found = False
    for logo_file in logo_files:
        if os.path.exists(logo_file):
            print(f"✅ {logo_file}")
            logo_found = True
        else:
            print(f"⚠️ {logo_file} - 文件不存在")
    
    if not logo_found:
        print("❌ 没有找到任何logo文件")
        all_ok = False
    
    return all_ok

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ 已清理 {dir_name}")
            except Exception as e:
                print(f"⚠️ 清理 {dir_name} 失败: {e}")

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "build_final.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            print("构建输出:")
            print(result.stdout)
            return True
        else:
            print("❌ 构建失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def test_exe():
    """测试生成的exe文件"""
    exe_path = "dist/批量图片自动转短视频_v3.8_修复版.exe"
    
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"✅ exe文件生成成功")
        print(f"📁 文件路径: {exe_path}")
        print(f"📊 文件大小: {file_size:.1f} MB")
        
        # 询问是否运行测试
        try:
            response = input("是否运行exe文件进行测试？(y/n): ").lower()
            if response == 'y':
                print("🚀 启动exe文件...")
                subprocess.Popen([exe_path])
        except KeyboardInterrupt:
            print("\n测试已取消")
        
        return True
    else:
        print("❌ exe文件未生成")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("批量图片自动转短视频 - 修复版构建脚本")
    print("解决图标和托盘问题")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请安装缺失的包")
        return False
    
    # 检查文件
    if not check_files():
        print("❌ 文件检查失败，请确保所有必要文件存在")
        return False
    
    # 清理构建目录
    clean_build()
    
    # 构建exe
    if not build_exe():
        print("❌ 构建失败")
        return False
    
    # 测试exe
    if not test_exe():
        print("❌ 测试失败")
        return False
    
    print("\n🎉 构建完成！")
    print("📋 修复内容:")
    print("  ✅ 解决了图标显示问题")
    print("  ✅ 解决了托盘功能问题")
    print("  ✅ 添加了资源文件路径处理")
    print("  ✅ 完善了依赖包打包")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("按回车键退出...")
    except KeyboardInterrupt:
        print("\n👋 构建被用户中断")
    except Exception as e:
        print(f"\n❌ 构建过程异常: {e}")
        input("按回车键退出...")
