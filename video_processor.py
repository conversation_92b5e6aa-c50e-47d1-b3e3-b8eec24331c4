#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片转视频处理器
项老师AI工作室 - 桌面图片转MP4工具
版本: v1.0
"""

import cv2
import numpy as np
import os
import glob
from PIL import Image, ImageEnhance
import random
import math
from pathlib import Path
import time

class VideoProcessor:
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        self.output_width = 1920
        self.output_height = 1080
        self.fps = 30
        self.duration_per_image = 3.0  # 每张图片显示时长（秒）
        self.transition_duration = 0.5  # 过渡效果时长（秒）

        # 模糊效果设置
        self.blur_enabled = False
        self.blur_start = 100
        self.blur_end = 0

        # 缩放效果设置
        self.scale_enabled = False
        self.scale_start = 150  # 初始缩放百分比
        self.scale_end = 100    # 目标缩放百分比

        # 粒子模糊效果设置
        self.particle_enabled = False
        self.particle_start = 500  # 初始粒子密度
        self.particle_end = 0      # 目标粒子密度

        # 亮度变化设置
        self.brightness_enabled = False
        self.brightness_start = -50  # 初始亮度
        self.brightness_end = 0      # 目标亮度

        # 饱和度变化设置
        self.saturation_enabled = False
        self.saturation_start = 20   # 初始饱和度百分比
        self.saturation_end = 120    # 目标饱和度百分比

        # 对比度变化设置
        self.contrast_enabled = False
        self.contrast_start = 0.8    # 初始对比度
        self.contrast_end = 1.2      # 目标对比度

        # 色调变化设置
        self.hue_enabled = False
        self.hue_start = 30          # 初始色调偏移（度）
        self.hue_end = 0             # 目标色调偏移（度）

        # 噪点变化设置
        self.noise_enabled = False
        self.noise_start = 80        # 初始噪点强度（百分比）
        self.noise_end = 0           # 目标噪点强度（百分比）

        # 锐化变化设置
        self.sharpen_enabled = False
        self.sharpen_start = 0       # 初始锐化强度
        self.sharpen_end = 2.0       # 目标锐化强度

        # 透明度变化设置
        self.alpha_enabled = False
        self.alpha_start = 30        # 初始透明度（百分比）
        self.alpha_end = 100         # 目标透明度（百分比）
        
    def get_desktop_images(self):
        """获取桌面上的所有图片文件"""
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        if not os.path.exists(desktop_path):
            desktop_path = os.path.join(os.path.expanduser("~"), "桌面")
        
        image_files = []
        for ext in self.supported_formats:
            pattern = os.path.join(desktop_path, f"*{ext}")
            image_files.extend(glob.glob(pattern, recursive=False))
            pattern = os.path.join(desktop_path, f"*{ext.upper()}")
            image_files.extend(glob.glob(pattern, recursive=False))

        # 去重并按文件名排序
        image_files = list(set(image_files))
        image_files.sort()
        return image_files
    
    def load_and_resize_image(self, image_path):
        """加载并调整图片尺寸"""
        try:
            # 使用PIL加载图片以支持更多格式
            pil_image = Image.open(image_path)
            
            # 转换为RGB模式
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # 计算缩放比例，保持宽高比
            img_width, img_height = pil_image.size
            scale_w = self.output_width / img_width
            scale_h = self.output_height / img_height
            scale = max(scale_w, scale_h)  # 使用较大的缩放比例确保填满画面
            
            # 计算新尺寸
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            
            # 调整尺寸
            pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为numpy数组
            img_array = np.array(pil_image)
            
            # 转换为OpenCV格式（BGR）
            cv_image = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            # 居中裁剪到目标尺寸
            if new_width > self.output_width or new_height > self.output_height:
                start_x = (new_width - self.output_width) // 2
                start_y = (new_height - self.output_height) // 2
                cv_image = cv_image[start_y:start_y + self.output_height, 
                                  start_x:start_x + self.output_width]
            
            return cv_image
            
        except Exception as e:
            print(f"加载图片失败 {image_path}: {e}")
            return None
    
    def apply_ken_burns_effect(self, image, frame_count, total_frames):
        """应用Ken Burns效果（缓慢缩放和平移）"""
        progress = frame_count / total_frames

        # 缩放参数（从1.0到1.2）
        start_scale = 1.0
        end_scale = 1.2
        current_scale = start_scale + (end_scale - start_scale) * progress

        # 随机平移方向
        if not hasattr(self, '_pan_direction'):
            self._pan_direction = random.choice(['left', 'right', 'up', 'down', 'diagonal_tl', 'diagonal_tr'])

        # 计算平移距离
        max_pan = 80  # 增加最大平移像素
        if self._pan_direction == 'left':
            pan_x = -max_pan * progress
            pan_y = 0
        elif self._pan_direction == 'right':
            pan_x = max_pan * progress
            pan_y = 0
        elif self._pan_direction == 'up':
            pan_y = -max_pan * progress
            pan_x = 0
        elif self._pan_direction == 'down':
            pan_y = max_pan * progress
            pan_x = 0
        elif self._pan_direction == 'diagonal_tl':  # 对角线左上
            pan_x = -max_pan * progress * 0.7
            pan_y = -max_pan * progress * 0.7
        else:  # diagonal_tr 对角线右上
            pan_x = max_pan * progress * 0.7
            pan_y = -max_pan * progress * 0.7

        return self._apply_scale_and_pan(image, current_scale, pan_x, pan_y)

    def apply_zoom_pulse_effect(self, image, frame_count, total_frames):
        """应用缩放脉冲效果（呼吸式缩放）"""
        progress = frame_count / total_frames

        # 使用正弦波创建脉冲效果
        pulse_frequency = 2.0  # 脉冲频率
        pulse_amplitude = 0.15  # 脉冲幅度
        base_scale = 1.1

        current_scale = base_scale + pulse_amplitude * math.sin(progress * pulse_frequency * 2 * math.pi)

        return self._apply_scale_and_pan(image, current_scale, 0, 0)

    def apply_rotation_effect(self, image, frame_count, total_frames):
        """应用旋转效果"""
        progress = frame_count / total_frames

        # 随机旋转方向
        if not hasattr(self, '_rotation_direction'):
            self._rotation_direction = random.choice([-1, 1])  # -1逆时针，1顺时针

        # 旋转角度（最大5度）
        max_angle = 5.0
        current_angle = max_angle * progress * self._rotation_direction

        # 轻微缩放
        scale = 1.0 + 0.1 * progress

        height, width = image.shape[:2]
        center = (width // 2, height // 2)

        # 创建旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, current_angle, scale)

        # 应用旋转
        rotated = cv2.warpAffine(image, rotation_matrix, (width, height),
                                borderMode=cv2.BORDER_REFLECT)

        return self._crop_to_output_size(rotated)

    def apply_slide_effect(self, image, frame_count, total_frames):
        """应用滑动效果"""
        progress = frame_count / total_frames

        # 随机滑动方向
        if not hasattr(self, '_slide_direction'):
            self._slide_direction = random.choice(['left_to_right', 'right_to_left',
                                                  'top_to_bottom', 'bottom_to_top'])

        # 创建黑色背景
        result = np.zeros((self.output_height, self.output_width, 3), dtype=np.uint8)

        # 调整图片到输出尺寸
        resized_image = self._resize_to_fit(image)

        if self._slide_direction == 'left_to_right':
            # 从左侧滑入
            slide_distance = int(self.output_width * (1 - progress))
            if slide_distance < self.output_width:
                result[:, slide_distance:] = resized_image[:, :self.output_width - slide_distance]
        elif self._slide_direction == 'right_to_left':
            # 从右侧滑入
            slide_distance = int(self.output_width * progress)
            if slide_distance > 0:
                result[:, :slide_distance] = resized_image[:, self.output_width - slide_distance:]
        elif self._slide_direction == 'top_to_bottom':
            # 从顶部滑入
            slide_distance = int(self.output_height * (1 - progress))
            if slide_distance < self.output_height:
                result[slide_distance:, :] = resized_image[:self.output_height - slide_distance, :]
        else:  # bottom_to_top
            # 从底部滑入
            slide_distance = int(self.output_height * progress)
            if slide_distance > 0:
                result[:slide_distance, :] = resized_image[self.output_height - slide_distance:, :]

        return result

    def apply_3d_flip_effect(self, image, frame_count, total_frames):
        """应用3D翻转效果"""
        progress = frame_count / total_frames

        # 随机翻转轴
        if not hasattr(self, '_flip_axis'):
            self._flip_axis = random.choice(['horizontal', 'vertical'])

        height, width = image.shape[:2]

        if self._flip_axis == 'horizontal':
            # 水平翻转效果
            perspective_factor = math.cos(progress * math.pi)
            new_width = int(width * abs(perspective_factor))

            if new_width > 0:
                # 调整宽度
                resized = cv2.resize(image, (new_width, height))

                # 创建结果图像
                result = np.zeros((height, width, 3), dtype=np.uint8)

                # 居中放置
                start_x = (width - new_width) // 2
                result[:, start_x:start_x + new_width] = resized

                # 如果是翻转的后半段，水平镜像
                if progress > 0.5:
                    result = cv2.flip(result, 1)
            else:
                result = np.zeros((height, width, 3), dtype=np.uint8)
        else:
            # 垂直翻转效果
            perspective_factor = math.cos(progress * math.pi)
            new_height = int(height * abs(perspective_factor))

            if new_height > 0:
                # 调整高度
                resized = cv2.resize(image, (width, new_height))

                # 创建结果图像
                result = np.zeros((height, width, 3), dtype=np.uint8)

                # 居中放置
                start_y = (height - new_height) // 2
                result[start_y:start_y + new_height, :] = resized

                # 如果是翻转的后半段，垂直镜像
                if progress > 0.5:
                    result = cv2.flip(result, 0)
            else:
                result = np.zeros((height, width, 3), dtype=np.uint8)

        return self._crop_to_output_size(result)
    
    def apply_color_shift_effect(self, image, frame_count, total_frames):
        """应用色彩变换效果"""
        progress = frame_count / total_frames

        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV).astype(np.float32)

        # 色相偏移（彩虹效果）
        hue_shift = int(30 * math.sin(progress * 2 * math.pi))
        hsv[:, :, 0] = (hsv[:, :, 0] + hue_shift) % 180

        # 饱和度变化
        saturation_factor = 1.0 + 0.3 * math.sin(progress * math.pi)
        hsv[:, :, 1] = np.clip(hsv[:, :, 1] * saturation_factor, 0, 255)

        # 转换回BGR
        result = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)

        # 轻微缩放
        scale = 1.0 + 0.05 * math.sin(progress * math.pi)
        return self._apply_scale_and_pan(result, scale, 0, 0)

    def apply_wave_distortion_effect(self, image, frame_count, total_frames):
        """应用波浪扭曲效果（优化版）"""
        progress = frame_count / total_frames
        height, width = image.shape[:2]

        # 使用numpy向量化操作，避免双重循环
        wave_amplitude = 5 * progress  # 减小波浪幅度
        wave_frequency = 0.01  # 减小波浪频率

        # 创建坐标网格
        y_coords, x_coords = np.mgrid[0:height, 0:width]

        # 向量化计算波浪偏移
        offset_x = wave_amplitude * np.sin(y_coords * wave_frequency + progress * 2 * np.pi)
        offset_y = wave_amplitude * np.sin(x_coords * wave_frequency + progress * 2 * np.pi) * 0.3

        # 创建映射
        map_x = (x_coords + offset_x).astype(np.float32)
        map_y = (y_coords + offset_y).astype(np.float32)

        # 应用扭曲
        result = cv2.remap(image, map_x, map_y, cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT)
        return self._crop_to_output_size(result)

    def apply_mosaic_effect(self, image, frame_count, total_frames):
        """应用马赛克效果（从模糊到清晰）"""
        progress = frame_count / total_frames

        # 马赛克强度（从强到弱）
        mosaic_strength = int(50 * (1 - progress)) + 1

        if mosaic_strength > 1:
            height, width = image.shape[:2]

            # 缩小图像
            small_height = height // mosaic_strength
            small_width = width // mosaic_strength

            if small_height > 0 and small_width > 0:
                # 缩小再放大，创建马赛克效果
                small_image = cv2.resize(image, (small_width, small_height), interpolation=cv2.INTER_LINEAR)
                result = cv2.resize(small_image, (width, height), interpolation=cv2.INTER_NEAREST)
            else:
                result = image.copy()
        else:
            result = image.copy()

        # 轻微缩放
        scale = 1.0 + 0.1 * progress
        return self._apply_scale_and_pan(result, scale, 0, 0)

    # 辅助方法
    def _apply_scale_and_pan(self, image, scale, pan_x, pan_y):
        """应用缩放和平移的通用方法"""
        height, width = image.shape[:2]

        # 缩放
        new_width = int(width * scale)
        new_height = int(height * scale)
        scaled_image = cv2.resize(image, (new_width, new_height))

        # 创建输出图像
        result = np.zeros((self.output_height, self.output_width, 3), dtype=np.uint8)

        # 计算居中位置并应用平移
        start_x = (new_width - self.output_width) // 2 + int(pan_x)
        start_y = (new_height - self.output_height) // 2 + int(pan_y)

        # 确保不超出边界
        start_x = max(0, min(start_x, new_width - self.output_width))
        start_y = max(0, min(start_y, new_height - self.output_height))

        # 裁剪并复制到结果图像
        if start_x + self.output_width <= new_width and start_y + self.output_height <= new_height:
            cropped = scaled_image[start_y:start_y + self.output_height,
                                  start_x:start_x + self.output_width]

            if cropped.shape[:2] == (self.output_height, self.output_width):
                result = cropped
            else:
                # 如果尺寸不匹配，居中放置
                h, w = cropped.shape[:2]
                y_offset = (self.output_height - h) // 2
                x_offset = (self.output_width - w) // 2
                result[y_offset:y_offset + h, x_offset:x_offset + w] = cropped

        return result

    def _resize_to_fit(self, image):
        """调整图片到输出尺寸"""
        height, width = image.shape[:2]
        scale_w = self.output_width / width
        scale_h = self.output_height / height
        scale = max(scale_w, scale_h)

        new_width = int(width * scale)
        new_height = int(height * scale)
        resized = cv2.resize(image, (new_width, new_height))

        # 居中裁剪
        start_x = (new_width - self.output_width) // 2
        start_y = (new_height - self.output_height) // 2

        return resized[start_y:start_y + self.output_height,
                      start_x:start_x + self.output_width]

    def _crop_to_output_size(self, image):
        """裁剪图像到输出尺寸"""
        height, width = image.shape[:2]

        if height == self.output_height and width == self.output_width:
            return image

        # 居中裁剪或填充
        result = np.zeros((self.output_height, self.output_width, 3), dtype=np.uint8)

        # 计算居中位置
        y_offset = (self.output_height - height) // 2
        x_offset = (self.output_width - width) // 2

        # 确保不超出边界
        src_y_start = max(0, -y_offset)
        src_y_end = min(height, self.output_height - y_offset)
        src_x_start = max(0, -x_offset)
        src_x_end = min(width, self.output_width - x_offset)

        dst_y_start = max(0, y_offset)
        dst_y_end = dst_y_start + (src_y_end - src_y_start)
        dst_x_start = max(0, x_offset)
        dst_x_end = dst_x_start + (src_x_end - src_x_start)

        result[dst_y_start:dst_y_end, dst_x_start:dst_x_end] = \
            image[src_y_start:src_y_end, src_x_start:src_x_end]

        return result

    def apply_fade_transition(self, image1, image2, progress):
        """应用淡入淡出过渡效果"""
        alpha = progress
        beta = 1.0 - alpha
        return cv2.addWeighted(image1, beta, image2, alpha, 0)
    
    def create_video_from_images(self, image_paths, output_path, effect_type='ken_burns',
                                progress_callback=None):
        """从图片创建视频"""
        if not image_paths:
            raise ValueError("没有找到图片文件")
        
        # 初始化视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(output_path, fourcc, self.fps, 
                                     (self.output_width, self.output_height))
        
        if not video_writer.isOpened():
            raise RuntimeError("无法创建视频文件")
        
        total_images = len(image_paths)
        frames_per_image = int(self.duration_per_image * self.fps)
        transition_frames = int(self.transition_duration * self.fps)
        
        try:
            for i, image_path in enumerate(image_paths):
                if progress_callback:
                    should_continue = progress_callback(f"处理图片 {i+1}/{total_images}: {os.path.basename(image_path)}")
                    if should_continue is False:
                        video_writer.release()  # 释放资源
                        break  # 停止处理
                
                # 加载当前图片
                current_image = self.load_and_resize_image(image_path)
                if current_image is None:
                    continue
                
                # 重置平移方向（为每张图片生成新的随机方向）
                if hasattr(self, '_pan_direction'):
                    delattr(self, '_pan_direction')
                
                # 生成当前图片的帧
                for frame_idx in range(frames_per_image):
                    frame = self._apply_effect(current_image, frame_idx, frames_per_image, effect_type)
                    video_writer.write(frame)
                
                # 添加过渡效果（除了最后一张图片）
                if i < total_images - 1:
                    next_image = self.load_and_resize_image(image_paths[i + 1])
                    if next_image is not None:
                        for trans_idx in range(transition_frames):
                            progress = trans_idx / transition_frames
                            
                            # 获取当前图片的最后一帧
                            current_frame = self._apply_effect(current_image, frames_per_image - 1,
                                                             frames_per_image, effect_type)
                            
                            # 应用淡入淡出过渡
                            transition_frame = self.apply_fade_transition(current_frame, 
                                                                        next_image, progress)
                            video_writer.write(transition_frame)
            
            if progress_callback:
                progress_callback("视频生成完成！")
                
        finally:
            video_writer.release()
        
        return True

    def _apply_effect(self, image, frame_idx, total_frames, effect_type):
        """应用指定的视频效果"""
        # 重置效果相关的实例变量（为每张图片生成新的随机参数）
        if frame_idx == 0:
            # 清除之前的效果参数
            for attr in ['_pan_direction', '_rotation_direction', '_slide_direction', '_flip_axis', '_random_effect', '_combo_effects']:
                if hasattr(self, attr):
                    delattr(self, attr)

        # 基础效果
        result = None
        if effect_type == 'ken_burns':
            result = self.apply_ken_burns_effect(image, frame_idx, total_frames)
        elif effect_type == 'zoom_pulse':
            result = self.apply_zoom_pulse_effect(image, frame_idx, total_frames)
        elif effect_type == 'rotation':
            result = self.apply_rotation_effect(image, frame_idx, total_frames)
        elif effect_type == 'slide':
            result = self.apply_slide_effect(image, frame_idx, total_frames)
        elif effect_type == '3d_flip':
            result = self.apply_3d_flip_effect(image, frame_idx, total_frames)
        elif effect_type == 'color_shift':
            result = self.apply_color_shift_effect(image, frame_idx, total_frames)
        elif effect_type == 'wave_distortion':
            result = self.apply_wave_distortion_effect(image, frame_idx, total_frames)
        elif effect_type == 'mosaic':
            result = self.apply_mosaic_effect(image, frame_idx, total_frames)

        # 随机效果
        elif effect_type == 'random_single':
            # 每张图片随机选择单一效果
            if not hasattr(self, '_random_effect'):
                effects = ['ken_burns', 'zoom_pulse', 'rotation', 'slide', 'color_shift', 'wave_distortion', 'mosaic']
                self._random_effect = random.choice(effects)
            # 直接调用对应的效果方法，避免递归
            if self._random_effect == 'ken_burns':
                result = self.apply_ken_burns_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'zoom_pulse':
                result = self.apply_zoom_pulse_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'rotation':
                result = self.apply_rotation_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'slide':
                result = self.apply_slide_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'color_shift':
                result = self.apply_color_shift_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'wave_distortion':
                result = self.apply_wave_distortion_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'mosaic':
                result = self.apply_mosaic_effect(image, frame_idx, total_frames)

        elif effect_type == 'random_mixed':
            # 每张图片随机选择组合效果
            if not hasattr(self, '_random_effect'):
                combo_effects = ['combo_elegant', 'combo_dynamic', 'combo_modern', 'combo_artistic']
                basic_effects = ['ken_burns', 'zoom_pulse', 'rotation', 'slide']
                all_effects = combo_effects + basic_effects
                self._random_effect = random.choice(all_effects)
            # 直接调用对应的效果方法，避免递归
            if self._random_effect == 'combo_elegant':
                result = self.apply_combo_elegant_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'combo_dynamic':
                result = self.apply_combo_dynamic_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'combo_modern':
                result = self.apply_combo_modern_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'combo_artistic':
                result = self.apply_combo_artistic_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'ken_burns':
                result = self.apply_ken_burns_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'zoom_pulse':
                result = self.apply_zoom_pulse_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'rotation':
                result = self.apply_rotation_effect(image, frame_idx, total_frames)
            elif self._random_effect == 'slide':
                result = self.apply_slide_effect(image, frame_idx, total_frames)

        # 组合效果
        elif effect_type == 'combo_elegant':
            result = self.apply_combo_elegant_effect(image, frame_idx, total_frames)
        elif effect_type == 'combo_dynamic':
            result = self.apply_combo_dynamic_effect(image, frame_idx, total_frames)
        elif effect_type == 'combo_modern':
            result = self.apply_combo_modern_effect(image, frame_idx, total_frames)
        elif effect_type == 'combo_artistic':
            result = self.apply_combo_artistic_effect(image, frame_idx, total_frames)

        else:  # static 或其他
            result = self._resize_to_fit(image)

        # 应用额外效果（多种效果同时应用）
        # 应用缩放效果（如果启用）
        if hasattr(self, 'scale_enabled') and self.scale_enabled:
            result = self.apply_scale_effect(result, frame_idx, total_frames)

        # 应用粒子模糊效果（如果启用）
        if hasattr(self, 'particle_enabled') and self.particle_enabled:
            result = self.apply_particle_blur_effect(result, frame_idx, total_frames)

        # 应用颜色效果（按顺序应用）
        # 应用亮度变化（如果启用）
        if hasattr(self, 'brightness_enabled') and self.brightness_enabled:
            result = self.apply_brightness_effect(result, frame_idx, total_frames)

        # 应用对比度变化（如果启用）
        if hasattr(self, 'contrast_enabled') and self.contrast_enabled:
            result = self.apply_contrast_effect(result, frame_idx, total_frames)

        # 应用饱和度变化（如果启用）
        if hasattr(self, 'saturation_enabled') and self.saturation_enabled:
            result = self.apply_saturation_effect(result, frame_idx, total_frames)

        # 应用色调变化（如果启用）
        if hasattr(self, 'hue_enabled') and self.hue_enabled:
            result = self.apply_hue_effect(result, frame_idx, total_frames)

        # 应用新增效果
        # 应用噪点变化（如果启用）
        if hasattr(self, 'noise_enabled') and self.noise_enabled:
            result = self.apply_noise_effect(result, frame_idx, total_frames)

        # 应用锐化变化（如果启用）
        if hasattr(self, 'sharpen_enabled') and self.sharpen_enabled:
            result = self.apply_sharpen_effect(result, frame_idx, total_frames)

        # 应用透明度变化（如果启用）
        if hasattr(self, 'alpha_enabled') and self.alpha_enabled:
            result = self.apply_alpha_effect(result, frame_idx, total_frames)

        # 应用模糊效果（如果启用）
        if hasattr(self, 'blur_enabled') and self.blur_enabled:
            result = self.apply_blur_effect(result, frame_idx, total_frames)

        return result

    def apply_scale_effect(self, image, frame_idx, total_frames):
        """应用缩放效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前缩放比例
            start_scale = self.scale_start / 100.0  # 转换为小数
            end_scale = self.scale_end / 100.0      # 转换为小数

            # 从start_scale线性变化到end_scale
            current_scale = start_scale + (end_scale - start_scale) * progress

            # 调试信息
            if frame_idx % 10 == 0:  # 每10帧打印一次
                print(f"缩放效果: 帧{frame_idx}/{total_frames}, 进度{progress:.2f}, 缩放{current_scale:.2f} ({self.scale_start}%→{self.scale_end}%)")

            # 获取图片尺寸
            height, width = image.shape[:2]

            # 计算缩放后的尺寸
            new_width = int(width * current_scale)
            new_height = int(height * current_scale)

            # 缩放图片
            scaled_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LINEAR)

            # 创建输出尺寸的画布（黑色背景）
            result = np.zeros((height, width, 3), dtype=np.uint8)

            # 计算居中位置
            start_y = max(0, (height - new_height) // 2)
            start_x = max(0, (width - new_width) // 2)

            # 计算实际可放置的区域
            end_y = min(height, start_y + new_height)
            end_x = min(width, start_x + new_width)

            # 计算源图片的对应区域
            src_start_y = max(0, (new_height - height) // 2) if new_height > height else 0
            src_start_x = max(0, (new_width - width) // 2) if new_width > width else 0
            src_end_y = src_start_y + (end_y - start_y)
            src_end_x = src_start_x + (end_x - start_x)

            # 将缩放后的图片（或其可见部分）放置在画布中心
            result[start_y:end_y, start_x:end_x] = scaled_image[src_start_y:src_end_y, src_start_x:src_end_x]

            return result

        except Exception as e:
            print(f"缩放效果应用失败: {e}")
            return image

    def apply_particle_blur_effect(self, image, frame_idx, total_frames):
        """应用粒子模糊效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前粒子密度
            current_particle = self.particle_start + (self.particle_end - self.particle_start) * progress
            current_particle = max(0, int(current_particle))  # 确保不小于0

            # 调试信息
            if frame_idx % 10 == 0:  # 每10帧打印一次
                print(f"粒子模糊: 帧{frame_idx}/{total_frames}, 进度{progress:.2f}, 粒子密度{current_particle} ({self.particle_start}→{self.particle_end})")

            # 如果粒子密度为0，直接返回原图
            if current_particle == 0:
                return image

            # 获取图片尺寸
            height, width = image.shape[:2]
            result = image.copy()

            # 计算粒子数量（基于密度和图片大小）
            particle_count = int((current_particle / 100.0) * (width * height) / 500)  # 每500像素一个粒子，增加密度
            particle_count = max(10, particle_count)  # 至少10个粒子

            # 生成随机粒子位置
            np.random.seed(frame_idx)  # 使用帧数作为种子，确保粒子位置一致
            particle_x = np.random.randint(0, width, particle_count)
            particle_y = np.random.randint(0, height, particle_count)

            # 粒子大小基于密度，增大粒子
            particle_size = max(3, int(current_particle / 10))  # 密度越高，粒子越大，最小3像素

            # 在每个粒子位置应用局部模糊
            for i in range(particle_count):
                x, y = particle_x[i], particle_y[i]

                # 计算粒子区域
                x1 = max(0, x - particle_size)
                y1 = max(0, y - particle_size)
                x2 = min(width, x + particle_size)
                y2 = min(height, y + particle_size)

                # 对粒子区域应用模糊
                if x2 > x1 and y2 > y1:
                    particle_region = result[y1:y2, x1:x2]
                    if particle_region.size > 0:
                        # 应用高斯模糊
                        blur_kernel = max(3, particle_size * 2 + 1)
                        if blur_kernel % 2 == 0:
                            blur_kernel += 1
                        blurred_region = cv2.GaussianBlur(particle_region, (blur_kernel, blur_kernel), 0)
                        result[y1:y2, x1:x2] = blurred_region

            return result

        except Exception as e:
            print(f"粒子模糊效果应用失败: {e}")
            return image

    def apply_blur_effect(self, image, frame_idx, total_frames):
        """应用模糊效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前模糊值
            current_blur = self.blur_start + (self.blur_end - self.blur_start) * progress
            current_blur = max(0, int(current_blur))  # 确保不小于0

            # 如果模糊值为0，直接返回原图
            if current_blur == 0:
                return image

            # 确保模糊值为奇数（OpenCV要求）
            kernel_size = current_blur
            if kernel_size % 2 == 0:
                kernel_size += 1

            # 应用高斯模糊
            blurred = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
            return blurred
        except Exception as e:
            print(f"模糊效果应用失败: {e}")
            return image

    def apply_brightness_effect(self, image, frame_idx, total_frames):
        """应用亮度变化效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前亮度值
            current_brightness = self.brightness_start + (self.brightness_end - self.brightness_start) * progress

            # 调试信息
            if frame_idx % 10 == 0:
                print(f"亮度变化: 帧{frame_idx}/{total_frames}, 进度{progress:.2f}, 亮度{current_brightness:.1f} ({self.brightness_start}→{self.brightness_end})")

            # 应用亮度调整
            result = cv2.convertScaleAbs(image, alpha=1.0, beta=current_brightness)
            return result

        except Exception as e:
            print(f"亮度效果应用失败: {e}")
            return image

    def apply_contrast_effect(self, image, frame_idx, total_frames):
        """应用对比度变化效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前对比度值
            current_contrast = self.contrast_start + (self.contrast_end - self.contrast_start) * progress

            # 调试信息
            if frame_idx % 10 == 0:
                print(f"对比度变化: 帧{frame_idx}/{total_frames}, 进度{progress:.2f}, 对比度{current_contrast:.2f} ({self.contrast_start}→{self.contrast_end})")

            # 应用对比度调整
            result = cv2.convertScaleAbs(image, alpha=current_contrast, beta=0)
            return result

        except Exception as e:
            print(f"对比度效果应用失败: {e}")
            return image

    def apply_saturation_effect(self, image, frame_idx, total_frames):
        """应用饱和度变化效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前饱和度值
            current_saturation = self.saturation_start + (self.saturation_end - self.saturation_start) * progress
            saturation_factor = current_saturation / 100.0

            # 调试信息
            if frame_idx % 10 == 0:
                print(f"饱和度变化: 帧{frame_idx}/{total_frames}, 进度{progress:.2f}, 饱和度{current_saturation:.1f}% ({self.saturation_start}→{self.saturation_end})")

            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV).astype(np.float32)

            # 调整饱和度通道
            hsv[:, :, 1] = hsv[:, :, 1] * saturation_factor
            hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)

            # 转换回BGR色彩空间
            result = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)
            return result

        except Exception as e:
            print(f"饱和度效果应用失败: {e}")
            return image

    def apply_hue_effect(self, image, frame_idx, total_frames):
        """应用色调变化效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前色调偏移值
            current_hue = self.hue_start + (self.hue_end - self.hue_start) * progress

            # 调试信息
            if frame_idx % 10 == 0:
                print(f"色调变化: 帧{frame_idx}/{total_frames}, 进度{progress:.2f}, 色调{current_hue:.1f}° ({self.hue_start}→{self.hue_end})")

            # 如果色调偏移为0，直接返回原图
            if abs(current_hue) < 0.1:
                return image

            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV).astype(np.float32)

            # 调整色调通道（H通道的范围是0-179）
            hue_shift = current_hue / 2.0  # 将度数转换为OpenCV的H通道范围
            hsv[:, :, 0] = (hsv[:, :, 0] + hue_shift) % 180

            # 转换回BGR色彩空间
            result = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)
            return result

        except Exception as e:
            print(f"色调效果应用失败: {e}")
            return image

    def apply_noise_effect(self, image, frame_idx, total_frames):
        """应用噪点变化效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前噪点强度
            current_noise = self.noise_start + (self.noise_end - self.noise_start) * progress

            # 调试信息
            if frame_idx % 10 == 0:
                print(f"噪点变化: 帧{frame_idx}/{total_frames}, 进度{progress:.2f}, 噪点{current_noise:.1f}% ({self.noise_start}→{self.noise_end})")

            # 如果噪点强度为0，直接返回原图
            if current_noise <= 0:
                return image

            # 生成噪点
            height, width = image.shape[:2]
            noise = np.random.normal(0, current_noise * 2.55, (height, width, 3))  # 转换为0-255范围

            # 应用噪点
            result = image.astype(np.float32) + noise
            result = np.clip(result, 0, 255).astype(np.uint8)

            return result

        except Exception as e:
            print(f"噪点效果应用失败: {e}")
            return image

    def apply_sharpen_effect(self, image, frame_idx, total_frames):
        """应用锐化变化效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前锐化强度
            current_sharpen = self.sharpen_start + (self.sharpen_end - self.sharpen_start) * progress

            # 调试信息
            if frame_idx % 10 == 0:
                print(f"锐化变化: 帧{frame_idx}/{total_frames}, 进度{progress:.2f}, 锐化{current_sharpen:.2f} ({self.sharpen_start}→{self.sharpen_end})")

            # 如果锐化强度为0，直接返回原图
            if current_sharpen <= 0:
                return image

            # 创建锐化核
            kernel = np.array([[-1, -1, -1],
                              [-1, 9 + current_sharpen, -1],
                              [-1, -1, -1]])

            # 应用锐化滤波
            result = cv2.filter2D(image, -1, kernel)

            return result

        except Exception as e:
            print(f"锐化效果应用失败: {e}")
            return image

    def apply_alpha_effect(self, image, frame_idx, total_frames):
        """应用透明度变化效果：从初始值线性变化到目标值"""
        try:
            progress = frame_idx / total_frames

            # 计算当前透明度
            current_alpha = self.alpha_start + (self.alpha_end - self.alpha_start) * progress
            alpha_factor = current_alpha / 100.0

            # 调试信息
            if frame_idx % 10 == 0:
                print(f"透明度变化: 帧{frame_idx}/{total_frames}, 进度{progress:.2f}, 透明度{current_alpha:.1f}% ({self.alpha_start}→{self.alpha_end})")

            # 如果透明度为100%，直接返回原图
            if alpha_factor >= 1.0:
                return image

            # 创建黑色背景
            height, width = image.shape[:2]
            background = np.zeros_like(image)

            # 应用透明度混合
            result = cv2.addWeighted(background, 1 - alpha_factor, image, alpha_factor, 0)

            return result

        except Exception as e:
            print(f"透明度效果应用失败: {e}")
            return image

    def apply_combo_elegant_effect(self, image, frame_idx, total_frames):
        """优雅组合效果：Ken Burns + 轻微色彩变换"""
        # 先应用Ken Burns效果
        ken_burns_result = self.apply_ken_burns_effect(image, frame_idx, total_frames)

        # 再应用轻微的色彩变换
        progress = frame_idx / total_frames
        hsv = cv2.cvtColor(ken_burns_result, cv2.COLOR_BGR2HSV).astype(np.float32)

        # 轻微的饱和度增强
        saturation_factor = 1.0 + 0.15 * math.sin(progress * math.pi)
        hsv[:, :, 1] = np.clip(hsv[:, :, 1] * saturation_factor, 0, 255)

        # 轻微的亮度调整
        brightness_factor = 1.0 + 0.05 * math.sin(progress * 2 * math.pi)
        hsv[:, :, 2] = np.clip(hsv[:, :, 2] * brightness_factor, 0, 255)

        return cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)

    def apply_combo_dynamic_effect(self, image, frame_idx, total_frames):
        """动感组合效果：旋转 + 脉冲缩放"""
        progress = frame_idx / total_frames
        height, width = image.shape[:2]
        center = (width // 2, height // 2)

        # 旋转参数
        if not hasattr(self, '_rotation_direction'):
            self._rotation_direction = random.choice([-1, 1])
        angle = 3.0 * progress * self._rotation_direction

        # 脉冲缩放参数
        pulse_frequency = 1.5
        base_scale = 1.05
        pulse_amplitude = 0.1
        scale = base_scale + pulse_amplitude * math.sin(progress * pulse_frequency * 2 * math.pi)

        # 创建变换矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, scale)

        # 应用变换
        transformed = cv2.warpAffine(image, rotation_matrix, (width, height),
                                   borderMode=cv2.BORDER_REFLECT)

        return self._crop_to_output_size(transformed)

    def apply_combo_modern_effect(self, image, frame_idx, total_frames):
        """现代组合效果：滑动 + 3D透视"""
        progress = frame_idx / total_frames

        # 前半段使用滑动效果
        if progress < 0.5:
            return self.apply_slide_effect(image, frame_idx, total_frames // 2)
        else:
            # 后半段使用3D透视效果
            adjusted_progress = (progress - 0.5) * 2
            height, width = image.shape[:2]

            # 3D透视变换
            perspective_factor = 0.8 + 0.2 * adjusted_progress

            # 定义源点和目标点
            src_points = np.float32([[0, 0], [width, 0], [width, height], [0, height]])

            # 创建透视效果
            offset = int(width * 0.1 * (1 - adjusted_progress))
            dst_points = np.float32([
                [offset, 0],
                [width - offset, 0],
                [width, height],
                [0, height]
            ])

            # 应用透视变换
            perspective_matrix = cv2.getPerspectiveTransform(src_points, dst_points)
            result = cv2.warpPerspective(image, perspective_matrix, (width, height))

            return self._crop_to_output_size(result)

    def apply_combo_artistic_effect(self, image, frame_idx, total_frames):
        """艺术组合效果：波浪扭曲 + 色彩变换"""
        # 先应用波浪扭曲
        wave_result = self.apply_wave_distortion_effect(image, frame_idx, total_frames)

        # 再应用色彩变换
        progress = frame_idx / total_frames
        hsv = cv2.cvtColor(wave_result, cv2.COLOR_BGR2HSV).astype(np.float32)

        # 色相循环变化
        hue_shift = int(60 * math.sin(progress * math.pi))
        hsv[:, :, 0] = (hsv[:, :, 0] + hue_shift) % 180

        # 饱和度波动
        saturation_factor = 1.0 + 0.4 * math.sin(progress * 3 * math.pi)
        hsv[:, :, 1] = np.clip(hsv[:, :, 1] * saturation_factor, 0, 255)

        return cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)

    def get_video_info(self, image_paths):
        """获取将要生成的视频信息"""
        if not image_paths:
            return None
        
        total_images = len(image_paths)
        frames_per_image = int(self.duration_per_image * self.fps)
        transition_frames = int(self.transition_duration * self.fps)
        
        total_frames = total_images * frames_per_image + (total_images - 1) * transition_frames
        total_duration = total_frames / self.fps
        
        return {
            'total_images': total_images,
            'total_duration': total_duration,
            'fps': self.fps,
            'resolution': f"{self.output_width}x{self.output_height}",
            'estimated_size_mb': total_frames * 0.1  # 粗略估算
        }
