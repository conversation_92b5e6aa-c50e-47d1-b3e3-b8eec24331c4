#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面图片转MP4工具 - 主程序
项老师AI工作室 - 传统企业AI自动化转型专家
版本: v3.8
创建时间: 2025年1月
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import subprocess

def check_dependencies():
    """检查依赖包"""
    required_packages = {
        'cv2': 'opencv-python',
        'PIL': 'Pillow',
        'numpy': 'numpy'
    }
    
    missing_packages = []
    
    for module, package in required_packages.items():
        try:
            __import__(module)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        return missing_packages
    return None

def install_dependencies(packages):
    """安装缺失的依赖包"""
    try:
        for package in packages:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装失败: {e}")
        return False

def show_welcome():
    """显示欢迎信息"""
    welcome_text = """
    🎬 批量图片自动转短视频 v3.1

    ✨ 功能特色:
    • 批量处理图片文件
    • 支持多种图片格式 (JPG, PNG, BMP, TIFF, WebP)
    • 6重线性变化效果 (模糊、缩放、亮度、饱和度、对比度、透明度)
    • 15种专业动态效果
    • 可调节视频参数 (分辨率、帧率、时长)
    • 高质量短视频输出

    🚀 使用说明:
    1. 选择包含图片的文件夹
    2. 设置视频效果和参数
    3. 点击"开始制作视频"按钮
    4. 批量自动生成短视频文件
    """
    print(welcome_text)

def main():
    """主函数"""
    print("=" * 60)
    print("批量图片自动转短视频")
    print("=" * 60)
    
    # 显示欢迎信息
    show_welcome()
    
    # 检查依赖
    print("🔍 检查依赖包...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        
        # 创建临时窗口询问是否安装
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        result = messagebox.askyesno(
            "缺少依赖包",
            f"程序需要以下依赖包:\n{chr(10).join(missing_packages)}\n\n是否自动安装？"
        )
        
        if result:
            print("📦 正在安装依赖包...")
            if install_dependencies(missing_packages):
                print("✅ 依赖包安装完成")
                messagebox.showinfo("成功", "依赖包安装完成，程序即将启动")
            else:
                print("❌ 依赖包安装失败")
                messagebox.showerror("错误", "依赖包安装失败，请手动安装")
                return
        else:
            print("❌ 用户取消安装，程序退出")
            return
        
        root.destroy()
    else:
        print("✅ 所有依赖包已安装")
    
    # 启动GUI
    try:
        print("🚀 启动图形界面...")
        from gui_interface import VideoCreatorGUI
        
        root = tk.Tk()
        
        # 设置窗口样式
        try:
            # 尝试设置现代主题
            import tkinter.ttk as ttk
            style = ttk.Style()
            available_themes = style.theme_names()
            if 'clam' in available_themes:
                style.theme_use('clam')
            elif 'alt' in available_themes:
                style.theme_use('alt')
        except:
            pass
        
        # 创建应用
        app = VideoCreatorGUI(root)
        
        # 设置窗口关闭事件
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出程序吗？"):
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 启动主循环
        print("✅ 程序启动成功")
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        messagebox.showerror("错误", f"程序启动失败: {e}")
    except Exception as e:
        print(f"❌ 程序运行错误: {e}")
        messagebox.showerror("错误", f"程序运行错误: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        input("按回车键退出...")
