#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的托盘功能测试
"""

import tkinter as tk
from tkinter import messagebox
import os

# 尝试导入托盘相关模块
try:
    import pystray
    from PIL import Image
    import threading
    TRAY_AVAILABLE = True
    print("✅ pystray模块可用")
except ImportError as e:
    TRAY_AVAILABLE = False
    print(f"❌ pystray模块不可用: {e}")

class SimpleTrayTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("简化托盘测试")
        self.root.geometry("300x200")
        
        self.tray_icon = None
        self.setup_ui()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """设置界面"""
        tk.Label(self.root, text="简化托盘测试", font=("Arial", 14)).pack(pady=20)
        
        status_text = "托盘功能可用" if TRAY_AVAILABLE else "托盘功能不可用"
        color = "green" if TRAY_AVAILABLE else "red"
        tk.Label(self.root, text=status_text, fg=color).pack(pady=10)
        
        if TRAY_AVAILABLE:
            tk.Button(self.root, text="最小化到托盘", command=self.minimize_to_tray).pack(pady=5)
        
        tk.Button(self.root, text="退出程序", command=self.quit_app).pack(pady=5)
    
    def on_closing(self):
        """窗口关闭事件"""
        if TRAY_AVAILABLE:
            result = messagebox.askyesno("关闭选项", "是否最小化到托盘？\n选择'否'将直接退出程序")
            if result:
                self.minimize_to_tray()
            else:
                self.quit_app()
        else:
            self.quit_app()
    
    def minimize_to_tray(self):
        """最小化到托盘"""
        if not TRAY_AVAILABLE:
            messagebox.showwarning("功能不可用", "托盘功能不可用")
            return
        
        try:
            print("🔄 创建托盘图标...")
            
            # 隐藏窗口
            self.root.withdraw()
            
            # 创建托盘图标
            if self.tray_icon is None:
                # 创建简单图标
                image = Image.new('RGB', (64, 64), color='blue')
                
                # 创建菜单
                menu = pystray.Menu(
                    pystray.MenuItem("显示窗口", self.show_window),
                    pystray.MenuItem("退出", self.quit_app)
                )
                
                # 创建托盘图标
                self.tray_icon = pystray.Icon(
                    "测试程序",
                    image,
                    "简化托盘测试",
                    menu
                )
                
                # 启动托盘
                def run_tray():
                    try:
                        print("🚀 启动托盘...")
                        self.tray_icon.run()
                        print("✅ 托盘启动成功")
                    except Exception as e:
                        print(f"❌ 托盘启动失败: {e}")
                
                threading.Thread(target=run_tray, daemon=True).start()
                print("✅ 托盘创建成功")
                messagebox.showinfo("成功", "已最小化到系统托盘")
        
        except Exception as e:
            print(f"❌ 托盘创建失败: {e}")
            messagebox.showerror("错误", f"托盘创建失败: {e}")
            self.show_window()
    
    def show_window(self, icon=None, item=None):
        """显示窗口"""
        print("🔄 显示窗口")
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
    
    def quit_app(self, icon=None, item=None):
        """退出程序"""
        print("🔄 退出程序")
        if self.tray_icon:
            self.tray_icon.stop()
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleTrayTest()
    app.run()
