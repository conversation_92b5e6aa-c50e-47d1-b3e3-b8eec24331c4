#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源文件路径处理工具
解决PyInstaller打包后资源文件路径问题
"""

import os
import sys

def resource_path(relative_path):
    """
    获取资源文件的绝对路径
    适用于开发环境和PyInstaller打包后的环境
    """
    try:
        # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
        print(f"🔍 检测到PyInstaller环境，资源基础路径: {base_path}")
    except AttributeError:
        # 开发环境，使用脚本所在目录
        base_path = os.path.abspath(".")
        print(f"🔍 开发环境，资源基础路径: {base_path}")
    
    full_path = os.path.join(base_path, relative_path)
    print(f"🔍 资源文件完整路径: {full_path}")
    print(f"🔍 文件是否存在: {os.path.exists(full_path)}")
    
    return full_path

def get_logo_path():
    """
    获取logo文件路径，优先使用ico格式
    """
    # 尝试不同的logo文件
    logo_files = ["logo.ico", "logo.png", "logo_correct.ico"]
    
    for logo_file in logo_files:
        logo_path = resource_path(logo_file)
        if os.path.exists(logo_path):
            print(f"✅ 找到logo文件: {logo_path}")
            return logo_path
    
    print("❌ 未找到任何logo文件")
    return None

def list_available_files():
    """
    列出可用的资源文件（调试用）
    """
    try:
        base_path = sys._MEIPASS
        print(f"📁 PyInstaller资源目录内容:")
        for file in os.listdir(base_path):
            if file.lower().endswith(('.ico', '.png', '.jpg')):
                print(f"  - {file}")
    except AttributeError:
        base_path = os.path.abspath(".")
        print(f"📁 开发环境目录内容:")
        for file in os.listdir(base_path):
            if file.lower().endswith(('.ico', '.png', '.jpg')):
                print(f"  - {file}")
