#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终构建脚本 - 确保所有依赖正确打包
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_dependencies():
    """检查所有依赖"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'pystray',
        'PIL',
        'cv2',
        'numpy',
        'tkinter'
    ]
    
    all_ok = True
    for package in required_packages:
        try:
            if package == 'PIL':
                from PIL import Image
            elif package == 'cv2':
                import cv2
            elif package == 'tkinter':
                import tkinter
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError as e:
            print(f"❌ {package}: {e}")
            all_ok = False
    
    return all_ok

def create_spec_file():
    """创建PyInstaller spec文件"""
    print("📝 创建spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('logo.ico', '.'),
        ('logo.png', '.'),
        ('tray_manager.py', '.'),
    ],
    hiddenimports=[
        'pystray',
        'pystray._win32',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'cv2',
        'numpy',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'threading',
        'six',
        'win32gui',
        'win32con',
        'win32api',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='批量图片自动转短视频_v3.7_终极版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.ico',
)
'''
    
    with open('build_final.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ spec文件创建完成")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    try:
        # 清理旧的构建文件
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # 使用spec文件构建
        cmd = [sys.executable, '-m', 'PyInstaller', 'build_final.spec']
        
        print(f"📋 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            
            # 检查生成的文件
            exe_path = "dist/批量图片自动转短视频_v3.7_终极版.exe"
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"📁 可执行文件: {exe_path}")
                print(f"📊 文件大小: {size:.1f} MB")
                return True
            else:
                print("❌ 可执行文件未生成")
                return False
        else:
            print("❌ 构建失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def copy_to_desktop():
    """复制到桌面"""
    try:
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        src = "dist/批量图片自动转短视频_v3.7_终极版.exe"
        dst = os.path.join(desktop, "批量图片自动转短视频_v3.7_终极版.exe")
        
        shutil.copy2(src, dst)
        print(f"✅ 已复制到桌面: {dst}")
        return True
        
    except Exception as e:
        print(f"❌ 复制到桌面失败: {e}")
        return False

def create_build_info():
    """创建构建信息"""
    build_info = f"""# 批量图片自动转短视频 v3.7 终极版

## 构建信息
- **构建时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **版本**: v3.7 终极版
- **作者**: 项老师AI工作室

## 功能特性
✅ **系统托盘功能**:
- 智能最小化到系统托盘
- 项老师logo托盘图标
- 右键托盘菜单 (显示窗口、退出程序)
- 后台运行处理

✅ **核心功能**:
- 默认帧率60FPS
- 6重线性变化效果
- 15种专业动态效果
- 批量处理功能
- 3行布局界面

## 依赖包版本
- pystray: 0.19.5
- Pillow: 10.4.0
- opencv-python: 4.12.0
- numpy: 最新版本

## 使用说明
1. 双击运行可执行文件
2. 选择图片文件夹
3. 设置视频参数
4. 点击"开始制作视频"
5. 可点击"最小化到托盘"后台运行

## 托盘功能说明
- 关闭窗口时会询问是否最小化到托盘
- 托盘图标使用项老师的专业logo
- 右键托盘图标可显示菜单
- 可在后台继续处理视频任务

---
© 2025 项老师AI工作室 | 传统企业AI自动化转型专家
"""
    
    with open("构建信息_v3.7.md", "w", encoding="utf-8") as f:
        f.write(build_info)
    
    print("📝 构建信息文件已创建")

def main():
    """主函数"""
    print("=" * 60)
    print("🎬 批量图片自动转短视频 - 终极版构建器")
    print("📱 项老师AI工作室出品 v3.7")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请先安装所有必要依赖")
        input("按回车键退出...")
        return False
    
    # 创建spec文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        print("❌ 构建失败")
        input("按回车键退出...")
        return False
    
    # 复制到桌面
    copy_to_desktop()
    
    # 创建构建信息
    create_build_info()
    
    print("\n" + "=" * 60)
    print("🎉 终极版构建完成！")
    print("✅ 所有托盘功能已正确打包")
    print("✅ 项老师logo已正确包含")
    print("✅ 所有依赖已正确打包")
    print("🚀 可执行文件已复制到桌面")
    print("=" * 60)
    
    input("\n按回车键退出...")
    return True

if __name__ == "__main__":
    main()
