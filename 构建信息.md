# 批量图片自动转短视频 - 构建信息

## 程序信息
- **名称**: 批量图片自动转短视频
- **版本**: v2.6
- **作者**: 项老师AI工作室
- **构建时间**: 2025-08-04 03:27:16

## 功能特色
- 🎬 批量处理图片文件
- 📱 支持多种图片格式 (JPG, PNG, BMP, TIFF, WebP)
- ✨ 10重线性变化效果 (模糊、缩放、粒子、亮度、饱和度、对比度、色调、噪点、锐化、透明度)
- 🎯 15种专业动态效果
- ⚙️ 可调节视频参数 (分辨率、帧率、时长)
- 🎥 高质量短视频输出
- 📊 实时队列进度显示
- 🔄 队列模式：一张图片生成一个视频

## 使用说明
1. 双击运行可执行文件
2. 选择包含图片的文件夹
3. 设置视频效果和参数
4. 选择输出目录
5. 点击"开始制作视频"按钮
6. 等待批量自动生成短视频文件

## 技术规格
- **开发语言**: Python 3.8+
- **图形界面**: Tkinter
- **图像处理**: OpenCV + PIL
- **视频编码**: MP4V
- **支持系统**: Windows 10/11

## 版权信息
© 2025 项老师AI工作室 | 传统企业AI自动化转型专家

---
**项老师AI工作室** - 定制超级总裁助理 AI应用培训 企业AI自动化转型
