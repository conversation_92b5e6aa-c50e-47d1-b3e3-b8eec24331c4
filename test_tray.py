#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试系统托盘功能
"""

import tkinter as tk
from tkinter import messagebox
import pystray
from PIL import Image
import threading
import os

class TrayTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("托盘功能测试")
        self.root.geometry("400x200")
        
        # 设置图标
        try:
            if os.path.exists("logo.ico"):
                self.root.iconbitmap("logo.ico")
                print("✅ 窗口图标设置成功")
            else:
                print("❌ logo.ico文件不存在")
        except Exception as e:
            print(f"❌ 设置窗口图标失败: {e}")
        
        self.tray_icon = None
        self.setup_ui()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.minimize_to_tray)
    
    def setup_ui(self):
        """设置界面"""
        tk.Label(self.root, text="托盘功能测试", font=("Arial", 16)).pack(pady=20)
        
        tk.Button(self.root, text="最小化到托盘", command=self.minimize_to_tray).pack(pady=10)
        tk.But<PERSON>(self.root, text="退出程序", command=self.quit_app).pack(pady=10)
        
        # 显示状态
        self.status_label = tk.Label(self.root, text="状态: 窗口显示中", fg="green")
        self.status_label.pack(pady=10)
    
    def minimize_to_tray(self):
        """最小化到系统托盘"""
        try:
            print("🔄 开始创建系统托盘...")
            
            # 隐藏主窗口
            self.root.withdraw()
            self.status_label.config(text="状态: 已最小化到托盘")
            
            # 创建托盘图标
            if self.tray_icon is None:
                # 加载图标
                if os.path.exists("logo.png"):
                    tray_image = Image.open("logo.png")
                    print("✅ 使用logo.png作为托盘图标")
                elif os.path.exists("logo.ico"):
                    tray_image = Image.open("logo.ico")
                    print("✅ 使用logo.ico作为托盘图标")
                else:
                    # 创建默认图标
                    tray_image = Image.new('RGB', (64, 64), color='#4A90E2')
                    print("⚠️ 使用默认蓝色图标")
                
                # 调整图标大小
                tray_image = tray_image.resize((64, 64), Image.Resampling.LANCZOS)
                
                # 创建托盘菜单
                menu = pystray.Menu(
                    pystray.MenuItem("显示主窗口", self.show_window),
                    pystray.MenuItem("退出程序", self.quit_app)
                )
                
                # 创建托盘图标
                self.tray_icon = pystray.Icon(
                    "托盘测试",
                    tray_image,
                    "托盘功能测试程序",
                    menu
                )
                
                # 在新线程中运行托盘图标
                def run_tray():
                    try:
                        print("🚀 启动托盘图标...")
                        self.tray_icon.run()
                        print("✅ 托盘图标运行成功")
                    except Exception as e:
                        print(f"❌ 托盘图标运行失败: {e}")
                
                threading.Thread(target=run_tray, daemon=True).start()
                print("✅ 系统托盘创建成功")
            
        except Exception as e:
            print(f"❌ 创建系统托盘失败: {e}")
            messagebox.showerror("错误", f"托盘功能创建失败: {e}")
            self.show_window()
    
    def show_window(self, icon=None, item=None):
        """显示主窗口"""
        print("🔄 显示主窗口")
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        self.status_label.config(text="状态: 窗口显示中")
    
    def quit_app(self, icon=None, item=None):
        """退出应用程序"""
        print("🔄 退出程序")
        if self.tray_icon:
            self.tray_icon.stop()
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        print("🚀 启动托盘测试程序")
        self.root.mainloop()

if __name__ == "__main__":
    app = TrayTest()
    app.run()
