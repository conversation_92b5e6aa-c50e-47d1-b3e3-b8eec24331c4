#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频效果演示工具
项老师AI工作室 - 桌面图片转MP4工具
版本: v1.0
"""

import cv2
import numpy as np
import os
from video_processor import VideoProcessor
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import threading

class EffectDemoGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🎨 视频效果演示工具 - 项老师AI工作室")
        self.root.geometry("900x700")
        
        self.processor = VideoProcessor()
        self.current_image = None
        self.demo_frames = []
        self.current_frame_index = 0
        self.is_playing = False
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎨 视频效果演示工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="🎛️ 控制面板", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 图片选择
        ttk.Label(control_frame, text="选择图片:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Button(control_frame, text="📁 浏览图片", 
                  command=self.load_image).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 效果选择
        ttk.Label(control_frame, text="视频效果:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.effect_var = tk.StringVar(value="ken_burns")
        effects = [
            ("Ken Burns效果", "ken_burns"),
            ("缩放脉冲效果", "zoom_pulse"),
            ("旋转效果", "rotation"),
            ("滑动效果", "slide"),
            ("3D翻转效果", "3d_flip"),
            ("色彩变换效果", "color_shift"),
            ("波浪扭曲效果", "wave_distortion"),
            ("马赛克效果", "mosaic"),
            ("静态显示", "static")
        ]
        
        for i, (name, value) in enumerate(effects):
            ttk.Radiobutton(control_frame, text=name, variable=self.effect_var, 
                           value=value, command=self.generate_demo).grid(
                           row=3+i, column=0, sticky=tk.W, pady=2)
        
        # 播放控制
        play_frame = ttk.Frame(control_frame)
        play_frame.grid(row=12, column=0, sticky=(tk.W, tk.E), pady=(20, 0))
        
        self.play_button = ttk.Button(play_frame, text="▶️ 播放", 
                                     command=self.toggle_play)
        self.play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(play_frame, text="🔄 重新生成", 
                  command=self.generate_demo).pack(side=tk.LEFT)
        
        # 右侧预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="🖼️ 效果预览", padding="10")
        preview_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
        # 预览画布
        self.preview_canvas = tk.Canvas(preview_frame, bg="black", width=640, height=360)
        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 进度条
        self.progress_var = tk.StringVar(value="请选择图片开始演示")
        progress_label = ttk.Label(preview_frame, textvariable=self.progress_var)
        progress_label.grid(row=1, column=0, pady=(10, 0))
        
        # 效果说明
        info_frame = ttk.LabelFrame(main_frame, text="ℹ️ 效果说明", padding="10")
        info_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.info_text = tk.Text(info_frame, height=4, wrap=tk.WORD)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        scrollbar = ttk.Scrollbar(info_frame, orient="vertical", command=self.info_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.info_text.configure(yscrollcommand=scrollbar.set)
        
        # 更新效果说明
        self.update_effect_info()
    
    def load_image(self):
        """加载图片"""
        filetypes = [
            ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.webp"),
            ("所有文件", "*.*")
        ]
        file_path = filedialog.askopenfilename(title="选择图片文件", filetypes=filetypes)
        
        if file_path:
            try:
                self.current_image = self.processor.load_and_resize_image(file_path)
                if self.current_image is not None:
                    self.progress_var.set(f"已加载: {os.path.basename(file_path)}")
                    self.generate_demo()
                else:
                    messagebox.showerror("错误", "无法加载图片文件")
            except Exception as e:
                messagebox.showerror("错误", f"加载图片失败: {e}")
    
    def generate_demo(self):
        """生成效果演示"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先选择图片")
            return
        
        self.is_playing = False
        self.play_button.config(text="▶️ 播放")
        
        try:
            effect_type = self.effect_var.get()
            self.progress_var.set("正在生成效果演示...")
            
            # 生成演示帧（2秒，30帧）
            total_frames = 60
            self.demo_frames = []
            
            for frame_idx in range(total_frames):
                frame = self.processor._apply_effect(self.current_image, frame_idx, 
                                                   total_frames, effect_type)
                self.demo_frames.append(frame)
            
            self.current_frame_index = 0
            self.update_preview()
            self.update_effect_info()
            self.progress_var.set(f"演示已生成 ({len(self.demo_frames)} 帧)")
            
        except Exception as e:
            messagebox.showerror("错误", f"生成演示失败: {e}")
            self.progress_var.set("生成失败")
    
    def update_preview(self):
        """更新预览画面"""
        if not self.demo_frames:
            return
        
        frame = self.demo_frames[self.current_frame_index]
        
        # 转换为PIL图像
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)
        
        # 调整尺寸适应画布
        canvas_width = self.preview_canvas.winfo_width()
        canvas_height = self.preview_canvas.winfo_height()
        
        if canvas_width > 1 and canvas_height > 1:
            # 保持宽高比缩放
            img_ratio = pil_image.width / pil_image.height
            canvas_ratio = canvas_width / canvas_height
            
            if img_ratio > canvas_ratio:
                new_width = canvas_width
                new_height = int(canvas_width / img_ratio)
            else:
                new_height = canvas_height
                new_width = int(canvas_height * img_ratio)
            
            pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 转换为Tkinter图像
        tk_image = ImageTk.PhotoImage(pil_image)
        
        # 清除画布并显示图像
        self.preview_canvas.delete("all")
        x = (canvas_width - pil_image.width) // 2
        y = (canvas_height - pil_image.height) // 2
        self.preview_canvas.create_image(x, y, anchor=tk.NW, image=tk_image)
        
        # 保持引用避免被垃圾回收
        self.preview_canvas.image = tk_image
    
    def toggle_play(self):
        """切换播放状态"""
        if not self.demo_frames:
            messagebox.showwarning("警告", "请先生成演示")
            return
        
        self.is_playing = not self.is_playing
        
        if self.is_playing:
            self.play_button.config(text="⏸️ 暂停")
            self.play_animation()
        else:
            self.play_button.config(text="▶️ 播放")
    
    def play_animation(self):
        """播放动画"""
        if not self.is_playing:
            return
        
        self.update_preview()
        
        # 下一帧
        self.current_frame_index = (self.current_frame_index + 1) % len(self.demo_frames)
        
        # 继续播放
        self.root.after(33, self.play_animation)  # 约30FPS
    
    def update_effect_info(self):
        """更新效果说明"""
        effect_type = self.effect_var.get()
        
        effect_descriptions = {
            "ken_burns": "Ken Burns效果：专业的缓慢缩放和平移动画，模拟摄像机推拉镜头效果。适合风景照片和人物照片，能够增加画面的动态感和电影感。",
            "zoom_pulse": "缩放脉冲效果：呼吸式的缩放动画，营造心跳般的节奏感。适合突出重点内容，创造有节奏感的视觉体验。",
            "rotation": "旋转效果：轻微的旋转动画配合缩放，增加动态感和立体感。适合艺术照片和创意内容，营造优雅的视觉效果。",
            "slide": "滑动效果：图片从不同方向滑入画面，现代化的切换动画。适合产品展示和介绍，具有清晰的方向感。",
            "3d_flip": "3D翻转效果：立体翻转动画，科技感十足的视觉效果。适合科技产品和现代设计，营造三维空间感。",
            "color_shift": "色彩变换效果：动态的色彩变化，彩虹般的色彩流动。适合艺术创作和时尚内容，增加视觉冲击力。",
            "wave_distortion": "波浪扭曲效果：水波般的扭曲动画，梦幻的视觉效果。适合创意视频和艺术表达，营造流动感。",
            "mosaic": "马赛克效果：从模糊到清晰的渐变，神秘感的视觉呈现。适合悬疑和揭示类内容，增加戏剧性。",
            "static": "静态显示：简单的图片切换，无特殊动画效果。适合正式文档和简洁展示，保持内容的严肃性。"
        }
        
        description = effect_descriptions.get(effect_type, "未知效果")
        
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, description)

def main():
    root = tk.Tk()
    app = EffectDemoGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
