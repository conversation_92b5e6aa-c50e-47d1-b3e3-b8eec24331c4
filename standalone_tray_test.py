#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立托盘功能测试
确保托盘功能完全可用
"""

import tkinter as tk
from tkinter import messagebox
import threading
import os
import sys

def test_imports():
    """测试所有必要的导入"""
    try:
        import pystray
        print("✅ pystray导入成功")
        
        from PIL import Image
        print("✅ PIL.Image导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def create_test_icon():
    """创建测试图标"""
    try:
        from PIL import Image
        
        # 尝试加载项老师logo
        if os.path.exists("logo.png"):
            image = Image.open("logo.png")
            print("✅ 使用logo.png")
        elif os.path.exists("logo.ico"):
            image = Image.open("logo.ico")
            print("✅ 使用logo.ico")
        else:
            # 创建测试图标
            image = Image.new('RGB', (64, 64), color='blue')
            print("⚠️ 使用默认蓝色图标")
        
        # 调整大小
        image = image.resize((64, 64), Image.Resampling.LANCZOS)
        return image
        
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")
        return None

class StandaloneTrayTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("独立托盘测试")
        self.root.geometry("400x300")
        
        # 设置图标
        try:
            if os.path.exists("logo.ico"):
                self.root.iconbitmap("logo.ico")
                print("✅ 窗口图标设置成功")
        except Exception as e:
            print(f"⚠️ 窗口图标设置失败: {e}")
        
        self.tray_icon = None
        self.setup_ui()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """设置界面"""
        tk.Label(self.root, text="独立托盘功能测试", font=("Arial", 16)).pack(pady=20)
        
        # 显示导入状态
        import_status = "✅ 依赖正常" if test_imports() else "❌ 依赖异常"
        tk.Label(self.root, text=f"依赖状态: {import_status}").pack(pady=10)
        
        # 显示图标状态
        icon_status = "✅ 图标可用" if create_test_icon() is not None else "❌ 图标异常"
        tk.Label(self.root, text=f"图标状态: {icon_status}").pack(pady=10)
        
        # 按钮
        tk.Button(self.root, text="测试托盘功能", command=self.test_tray, 
                 bg="lightblue", font=("Arial", 12)).pack(pady=10)
        
        tk.Button(self.root, text="最小化到托盘", command=self.minimize_to_tray,
                 bg="lightgreen", font=("Arial", 12)).pack(pady=10)
        
        tk.Button(self.root, text="退出程序", command=self.quit_app,
                 bg="lightcoral", font=("Arial", 12)).pack(pady=10)
        
        # 状态显示
        self.status_label = tk.Label(self.root, text="状态: 程序运行中", fg="green")
        self.status_label.pack(pady=20)
    
    def test_tray(self):
        """测试托盘功能"""
        try:
            import pystray
            from PIL import Image
            
            # 创建测试图标
            image = create_test_icon()
            if image is None:
                messagebox.showerror("错误", "无法创建图标")
                return
            
            # 创建简单菜单
            menu = pystray.Menu(
                pystray.MenuItem("测试项目", lambda: messagebox.showinfo("测试", "托盘菜单工作正常！"))
            )
            
            # 创建托盘图标
            icon = pystray.Icon("测试", image, "托盘测试", menu)
            
            messagebox.showinfo("成功", "托盘功能测试成功！\n所有组件都可以正常创建")
            
        except Exception as e:
            messagebox.showerror("错误", f"托盘功能测试失败:\n{e}")
    
    def minimize_to_tray(self):
        """最小化到托盘"""
        try:
            import pystray
            from PIL import Image
            
            print("🔄 开始最小化到托盘...")
            
            # 创建图标
            image = create_test_icon()
            if image is None:
                messagebox.showerror("错误", "无法创建托盘图标")
                return
            
            # 隐藏窗口
            self.root.withdraw()
            self.status_label.config(text="状态: 已最小化到托盘")
            
            # 创建托盘菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示窗口", self.show_window),
                pystray.MenuItem("退出程序", self.quit_app)
            )
            
            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                "独立托盘测试",
                image,
                "独立托盘测试程序",
                menu
            )
            
            # 在新线程中运行托盘
            def run_tray():
                try:
                    print("🚀 启动托盘图标...")
                    self.tray_icon.run()
                    print("✅ 托盘图标运行成功")
                except Exception as e:
                    print(f"❌ 托盘运行失败: {e}")
                    # 恢复窗口
                    self.root.after(0, self.show_window)
            
            threading.Thread(target=run_tray, daemon=True).start()
            
            # 显示成功消息
            messagebox.showinfo("成功", "程序已最小化到系统托盘！\n右键托盘图标查看菜单")
            
        except Exception as e:
            print(f"❌ 最小化失败: {e}")
            messagebox.showerror("错误", f"最小化到托盘失败:\n{e}")
            self.show_window()
    
    def show_window(self, icon=None, item=None):
        """显示窗口"""
        print("🔄 显示窗口")
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        self.status_label.config(text="状态: 程序运行中")
    
    def quit_app(self, icon=None, item=None):
        """退出程序"""
        print("🔄 退出程序")
        if self.tray_icon:
            self.tray_icon.stop()
        self.root.quit()
        self.root.destroy()
    
    def on_closing(self):
        """窗口关闭事件"""
        result = messagebox.askyesno("关闭确认", "是否最小化到托盘？\n选择'否'将直接退出程序")
        if result:
            self.minimize_to_tray()
        else:
            self.quit_app()
    
    def run(self):
        """运行程序"""
        print("🚀 启动独立托盘测试程序")
        self.root.mainloop()

def main():
    """主函数"""
    print("=" * 50)
    print("🎯 独立托盘功能测试")
    print("=" * 50)
    
    # 检查环境
    if not test_imports():
        print("❌ 环境检查失败，请安装必要依赖")
        input("按回车键退出...")
        return
    
    # 启动测试程序
    app = StandaloneTrayTest()
    app.run()

if __name__ == "__main__":
    main()
