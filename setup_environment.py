#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查和依赖安装脚本
批量图片自动转短视频 v3.1
项老师AI工作室出品
"""

import sys
import subprocess
import importlib
import os

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro} (符合要求)")
        return True
    else:
        print(f"❌ Python版本: {version.major}.{version.minor}.{version.micro} (需要Python 3.8+)")
        return False

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def check_and_install_package(package_name, import_name=None):
    """检查并安装包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"⚠️ {package_name} 未安装，正在安装...")
        return install_package(package_name)

def check_required_packages():
    """检查所有必需的包"""
    print("\n🔍 检查必需的Python包...")
    
    packages = [
        ("opencv-python", "cv2"),
        ("numpy", "numpy"),
        ("Pillow", "PIL"),
        ("pystray", "pystray"),
        ("pywin32", "win32gui"),
        ("imageio", "imageio"),
        ("imageio-ffmpeg", "imageio_ffmpeg"),
        ("psutil", "psutil"),
        ("six", "six")
    ]
    
    all_success = True
    for package_name, import_name in packages:
        if not check_and_install_package(package_name, import_name):
            all_success = False
    
    return all_success

def check_system_requirements():
    """检查系统要求"""
    print("\n🔍 检查系统要求...")
    
    # 检查操作系统
    if sys.platform.startswith('win'):
        print("✅ 操作系统: Windows (支持)")
    else:
        print(f"⚠️ 操作系统: {sys.platform} (可能不完全支持)")
    
    # 检查必要文件
    required_files = ["logo.ico", "logo.png"]
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"✅ 文件存在: {file_name}")
        else:
            print(f"⚠️ 文件缺失: {file_name}")

def test_functionality():
    """测试核心功能"""
    print("\n🧪 测试核心功能...")
    
    try:
        # 测试OpenCV
        import cv2
        print(f"✅ OpenCV版本: {cv2.__version__}")
        
        # 测试PIL
        from PIL import Image
        print("✅ PIL图像处理功能正常")
        
        # 测试托盘功能
        import pystray
        print("✅ 系统托盘功能可用")
        
        # 测试Windows API
        try:
            import win32gui
            print("✅ Windows API可用")
        except ImportError:
            print("⚠️ Windows API不可用（可能影响部分功能）")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def create_desktop_shortcut():
    """创建桌面快捷方式"""
    try:
        import win32com.client
        
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        shortcut_path = os.path.join(desktop, "批量图片自动转短视频.lnk")
        target_path = os.path.join(os.getcwd(), "main.py")
        
        shell = win32com.client.Dispatch("WScript.Shell")
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target_path}"'
        shortcut.WorkingDirectory = os.getcwd()
        shortcut.IconLocation = os.path.join(os.getcwd(), "logo.ico")
        shortcut.save()
        
        print(f"✅ 桌面快捷方式已创建: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"⚠️ 创建桌面快捷方式失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎬 批量图片自动转短视频 - 环境配置工具")
    print("📱 项老师AI工作室出品 v3.1")
    print("=" * 60)
    
    success = True
    
    # 检查Python版本
    if not check_python_version():
        success = False
    
    # 检查系统要求
    check_system_requirements()
    
    # 检查并安装包
    if not check_required_packages():
        success = False
    
    # 测试功能
    if not test_functionality():
        success = False
    
    # 创建快捷方式
    create_desktop_shortcut()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 环境配置完成！所有依赖已安装，功能测试通过")
        print("🚀 您现在可以运行 python main.py 启动程序")
    else:
        print("❌ 环境配置存在问题，请检查上述错误信息")
    print("=" * 60)
    
    input("\n按回车键退出...")
    return success

if __name__ == "__main__":
    main()
