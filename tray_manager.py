#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统托盘管理器
真正实现托盘功能
"""

import tkinter as tk
from tkinter import messagebox
import threading
import os
from PIL import Image
from resource_utils import resource_path, get_logo_path, list_available_files

try:
    import pystray
    TRAY_AVAILABLE = True
except ImportError:
    TRAY_AVAILABLE = False

class TrayManager:
    def __init__(self, main_window):
        self.main_window = main_window
        self.tray_icon = None
        self.is_in_tray = False
        
    def create_tray_icon(self):
        """创建系统托盘图标"""
        if not TRAY_AVAILABLE:
            messagebox.showwarning("托盘不可用", "系统托盘功能不可用，程序将最小化到任务栏")
            self.main_window.iconify()
            return False
        
        try:
            print("🔍 开始创建托盘图标...")
            list_available_files()  # 调试：列出可用文件

            # 获取logo文件路径
            logo_path = get_logo_path()

            if logo_path:
                image = Image.open(logo_path)
                print(f"✅ 成功加载项老师logo: {logo_path}")
            else:
                # 创建默认图标
                image = Image.new('RGB', (64, 64), color='#4A90E2')
                print("⚠️ 未找到logo文件，使用默认图标")
            
            # 调整图标大小
            image = image.resize((64, 64), Image.Resampling.LANCZOS)
            
            # 创建托盘菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示主窗口", self.show_window),
                pystray.MenuItem("退出程序", self.quit_application)
            )
            
            # 创建托盘图标（添加双击事件）
            self.tray_icon = pystray.Icon(
                "批量图片自动转短视频",
                image,
                "批量图片自动转短视频 v3.8 - 项老师AI工作室",
                menu,
                default_action=self.show_window  # 双击时显示窗口
            )
            
            return True
            
        except Exception as e:
            print(f"❌ 创建托盘图标失败: {e}")
            messagebox.showerror("托盘错误", f"创建托盘图标失败: {e}")
            return False
    
    def minimize_to_tray(self):
        """最小化到系统托盘"""
        if not TRAY_AVAILABLE:
            print("⚠️ 托盘功能不可用")
            return False

        # 如果已经在托盘中，直接返回
        if self.is_in_tray:
            print("ℹ️ 程序已经在托盘中")
            return True

        if not self.create_tray_icon():
            return False

        try:
            # 隐藏主窗口
            self.main_window.withdraw()
            self.is_in_tray = True

            # 在新线程中运行托盘
            def run_tray():
                try:
                    print("🚀 启动系统托盘...")
                    self.tray_icon.run()
                    print("✅ 系统托盘运行成功")
                except Exception as e:
                    print(f"❌ 系统托盘运行失败: {e}")
                    # 如果托盘失败，恢复窗口
                    self.main_window.after(0, self.show_window)

            # 检查是否已有托盘线程在运行
            if not hasattr(self, 'tray_thread') or not self.tray_thread.is_alive():
                self.tray_thread = threading.Thread(target=run_tray, daemon=True)
                self.tray_thread.start()

                # 显示提示（只在首次最小化时显示）
                messagebox.showinfo("最小化成功", "程序已最小化到系统托盘\n右键托盘图标可以显示菜单")

            print("✅ 程序已最小化到系统托盘")
            return True

        except Exception as e:
            print(f"❌ 最小化到托盘失败: {e}")
            messagebox.showerror("托盘错误", f"最小化失败: {e}")
            return False
    
    def show_window(self, icon=None, item=None):
        """显示主窗口（支持双击托盘图标）"""
        try:
            print("🔄 从托盘恢复主窗口")

            # 确保窗口状态正确
            if self.main_window.state() == 'withdrawn':
                self.main_window.deiconify()
            elif self.main_window.state() == 'iconic':
                self.main_window.deiconify()

            # 显示并激活窗口
            self.main_window.lift()
            self.main_window.focus_force()

            # 临时置顶确保窗口显示在最前面
            self.main_window.attributes('-topmost', True)
            self.main_window.after(100, lambda: self.main_window.attributes('-topmost', False))

            # 更新状态
            self.is_in_tray = False
            print("✅ 主窗口已从托盘恢复")

        except Exception as e:
            print(f"❌ 显示主窗口失败: {e}")
            # 如果恢复失败，尝试强制显示
            try:
                self.main_window.state('normal')
                self.main_window.lift()
            except:
                pass
    
    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        try:
            print("🔄 退出程序")
            
            # 停止托盘图标
            if self.tray_icon:
                self.tray_icon.stop()
                print("✅ 托盘图标已停止")
            
            # 退出主程序
            self.main_window.quit()
            self.main_window.destroy()
            print("✅ 程序已退出")
            
        except Exception as e:
            print(f"❌ 退出程序失败: {e}")
            import sys
            sys.exit(0)
    
    def on_window_close(self):
        """窗口关闭事件处理"""
        if TRAY_AVAILABLE:
            result = messagebox.askyesnocancel(
                "关闭选项",
                "选择关闭方式：\n\n是：最小化到系统托盘\n否：直接退出程序\n取消：返回程序"
            )

            if result is True:  # 是 - 最小化到托盘
                self.minimize_to_tray()
                # 不调用quit或destroy，只是最小化到托盘
            elif result is False:  # 否 - 直接退出
                self.quit_application()
            # 取消 - 什么都不做，窗口保持打开
        else:
            # 托盘不可用，直接询问是否退出
            result = messagebox.askyesno("退出确认", "确定要退出程序吗？")
            if result:
                self.quit_application()
