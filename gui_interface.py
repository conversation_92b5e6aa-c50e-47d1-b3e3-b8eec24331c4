#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图形用户界面
批量图片自动转短视频
版本: v3.8
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import glob
import random
from datetime import datetime
from video_processor import VideoProcessor
from PIL import Image, ImageTk
from tray_manager import TrayManager, TRAY_AVAILABLE
from resource_utils import resource_path, get_logo_path, list_available_files
import sys

class VideoCreatorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("批量图片自动转短视频 v3.1 - 项老师AI工作室")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # 设置窗口图标为项老师logo（多种方式尝试）
        self.set_window_icon()

        # 系统托盘管理器（必须在设置关闭事件之前初始化）
        self.tray_manager = TrayManager(self.root)

        # 设置窗口关闭事件（使用托盘管理器处理）
        self.root.protocol("WM_DELETE_WINDOW", self.tray_manager.on_window_close)

        self.processor = VideoProcessor()
        self.image_paths = []
        self.is_processing = False
        self.is_paused = False
        self.processing_thread = None
        self.task_queue = []  # 任务队列
        self.current_task_index = 0  # 当前任务索引

        # 队列进度相关
        self.queue_start_time = None
        self.current_image_start_time = None
        self.completed_count = 0
        self.total_count = 0

        # 连续模式变量
        self.continuous_mode_var = tk.BooleanVar(value=False)
        
        self.setup_ui()

    def set_window_icon(self):
        """设置窗口图标（支持PyInstaller打包）"""
        icon_set = False

        print("🔍 开始设置窗口图标...")
        list_available_files()  # 调试：列出可用文件

        # 获取logo文件路径
        logo_path = get_logo_path()

        if logo_path:
            # 方法1：尝试使用ico文件
            if logo_path.endswith('.ico'):
                try:
                    self.root.iconbitmap(logo_path)
                    print(f"✅ 成功使用ico文件作为窗口图标: {logo_path}")
                    icon_set = True
                except Exception as e:
                    print(f"⚠️ ico文件设置失败: {e}")

            # 方法2：使用png文件或ico文件失败时的备用方案
            if not icon_set:
                try:
                    logo_img = Image.open(logo_path)
                    logo_img = logo_img.resize((32, 32), Image.Resampling.LANCZOS)
                    self.logo_photo = ImageTk.PhotoImage(logo_img)
                    self.root.iconphoto(True, self.logo_photo)
                    print(f"✅ 成功使用图片文件作为窗口图标: {logo_path}")
                    icon_set = True
                except Exception as e:
                    print(f"⚠️ 图片文件设置失败: {e}")
        else:
            print("⚠️ 未找到logo文件")

        # 方法3：尝试使用默认方式
        if not icon_set:
            try:
                self.root.iconbitmap(default="logo.ico")
                print("✅ 方法3成功：使用默认方式设置图标")
                icon_set = True
            except Exception as e:
                print(f"⚠️ 方法3失败：{e}")

        # 方法4：尝试从当前目录的不同路径
        if not icon_set:
            icon_paths = ["./logo.ico", "./logo.png", "logo.ico", "logo.png"]
            for path in icon_paths:
                try:
                    if os.path.exists(path):
                        if path.endswith('.ico'):
                            self.root.iconbitmap(path)
                        else:
                            img = Image.open(path)
                            img = img.resize((32, 32), Image.Resampling.LANCZOS)
                            photo = ImageTk.PhotoImage(img)
                            self.root.iconphoto(True, photo)
                        print(f"✅ 方法4成功：使用{path}作为窗口图标")
                        icon_set = True
                        break
                except Exception as e:
                    print(f"⚠️ 方法4失败({path})：{e}")

        if not icon_set:
            print("❌ 所有图标设置方法都失败，使用系统默认图标")

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 移除无用标题，节省空间
        
        # 文件夹加载（去掉标题，节省空间）
        folder_frame = ttk.Frame(main_frame, padding="5")
        folder_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        folder_frame.columnconfigure(1, weight=1)

        # 文件夹路径显示和加载按钮
        ttk.Button(folder_frame, text="选择文件夹",
                  command=self.load_folder_images).grid(row=0, column=0, padx=(0, 10))

        self.folder_path_var = tk.StringVar(value="请选择包含图片的文件夹...")
        folder_label = ttk.Label(folder_frame, textvariable=self.folder_path_var,
                                foreground="gray", font=("", 9))
        folder_label.grid(row=0, column=1, sticky=(tk.W, tk.E))

        self.image_count_var = tk.StringVar(value="")
        count_label = ttk.Label(folder_frame, textvariable=self.image_count_var,
                               foreground="blue", font=("", 9))
        count_label.grid(row=0, column=2, padx=(10, 0))
        
        # 设置区域（去掉标题）
        settings_frame = ttk.Frame(main_frame, padding="5")
        settings_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 基础设置（一行显示：效果、分辨率、时长、帧率）
        basic_frame = ttk.Frame(settings_frame)
        basic_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        basic_frame.columnconfigure(1, weight=1)  # 效果列可扩展
        basic_frame.columnconfigure(3, weight=1)  # 分辨率列可扩展

        # 效果选择
        ttk.Label(basic_frame, text="效果:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.effect_var = tk.StringVar(value="波浪扭曲效果")
        # 效果映射：显示名称 -> 内部代码
        self.effect_mapping = {
            "肯伯恩斯效果": "ken_burns",
            "缩放脉冲效果": "zoom_pulse",
            "旋转效果": "rotation",
            "滑动效果": "slide",
            "3D翻转效果": "3d_flip",
            "色彩变换效果": "color_shift",
            "波浪扭曲效果": "wave_distortion",
            "马赛克效果": "mosaic",
            "随机单一效果": "random_single",
            "随机混合效果": "random_mixed",
            "优雅组合": "combo_elegant",
            "动感组合": "combo_dynamic",
            "现代组合": "combo_modern",
            "艺术组合": "combo_artistic",
            "静态显示": "static"
        }

        effect_values = list(self.effect_mapping.keys())
        effect_combo = ttk.Combobox(basic_frame, textvariable=self.effect_var,
                                   values=effect_values, state="readonly", width=12)
        effect_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))

        # 分辨率选择
        ttk.Label(basic_frame, text="分辨率:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.resolution_var = tk.StringVar(value="1080x1920 (1080p竖屏)")
        resolution_values = [
            "1920x1080 (1080p横屏)",
            "1280x720 (720p横屏)",
            "3840x2160 (4K横屏)",
            "1080x1920 (1080p竖屏)",
            "720x1280 (720p竖屏)",
            "2160x3840 (4K竖屏)",
            "1080x1080 (正方形)",
            "720x720 (正方形小)",
            "2048x2048 (正方形大)"
        ]
        resolution_combo = ttk.Combobox(basic_frame, textvariable=self.resolution_var,
                                       values=resolution_values, state="readonly", width=16)
        resolution_combo.grid(row=0, column=3, sticky=tk.W, padx=(0, 10))

        # 时长设置
        ttk.Label(basic_frame, text="时长(秒):").grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        import random
        random_duration = 3.0 + random.random()  # 3.0-4.0秒之间
        self.duration_var = tk.StringVar(value=f"{random_duration:.1f}")
        duration_entry = ttk.Entry(basic_frame, textvariable=self.duration_var, width=6)
        duration_entry.grid(row=0, column=5, sticky=tk.W, padx=(0, 10))

        # 帧率设置
        ttk.Label(basic_frame, text="帧率:").grid(row=0, column=6, sticky=tk.W, padx=(0, 5))
        self.fps_var = tk.StringVar(value="60")
        fps_combo = ttk.Combobox(basic_frame, textvariable=self.fps_var,
                                values=["24", "30", "60"], state="readonly", width=6)
        fps_combo.grid(row=0, column=7, sticky=tk.W)

        # 线性变化效果（3行2列布局，整齐排列）
        effects_frame = ttk.Frame(settings_frame)
        effects_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        effects_frame.columnconfigure(0, weight=1)
        effects_frame.columnconfigure(1, weight=1)

        # 第1行：模糊变化 + 对比度变化
        # 模糊变化（左侧）
        self.blur_enabled_var = tk.BooleanVar(value=True)
        blur_check = ttk.Checkbutton(effects_frame, text="☑️ 模糊变化", variable=self.blur_enabled_var)
        blur_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        blur_frame = ttk.Frame(effects_frame)
        blur_frame.grid(row=0, column=0, sticky=tk.W, padx=(120, 0), pady=(0, 5))
        ttk.Label(blur_frame, text="从:").grid(row=0, column=0, sticky=tk.W)
        self.blur_start_var = tk.StringVar(value="100")
        ttk.Spinbox(blur_frame, textvariable=self.blur_start_var, from_=0, to=200, width=5).grid(row=0, column=1, padx=(2, 5))
        ttk.Label(blur_frame, text="到:").grid(row=0, column=2, sticky=tk.W)
        self.blur_end_var = tk.StringVar(value="0")
        ttk.Spinbox(blur_frame, textvariable=self.blur_end_var, from_=0, to=200, width=5).grid(row=0, column=3, padx=(2, 0))

        # 对比度变化（右侧）
        self.contrast_enabled_var = tk.BooleanVar(value=True)
        contrast_check = ttk.Checkbutton(effects_frame, text="☑️ 对比度变化", variable=self.contrast_enabled_var)
        contrast_check.grid(row=0, column=1, sticky=tk.W, pady=(0, 5))

        contrast_frame = ttk.Frame(effects_frame)
        contrast_frame.grid(row=0, column=1, sticky=tk.W, padx=(140, 0), pady=(0, 5))
        ttk.Label(contrast_frame, text="从:").grid(row=0, column=0, sticky=tk.W)
        self.contrast_start_var = tk.StringVar(value="0.6")
        ttk.Spinbox(contrast_frame, textvariable=self.contrast_start_var, from_=0.1, to=5.0, width=5).grid(row=0, column=1, padx=(2, 5))
        ttk.Label(contrast_frame, text="到:").grid(row=0, column=2, sticky=tk.W)
        self.contrast_end_var = tk.StringVar(value="1.2")
        ttk.Spinbox(contrast_frame, textvariable=self.contrast_end_var, from_=0.1, to=5.0, width=5).grid(row=0, column=3, padx=(2, 0))

        # 第2行：缩放变化 + 透明度变化
        # 缩放变化（左侧）
        self.scale_enabled_var = tk.BooleanVar(value=True)
        scale_check = ttk.Checkbutton(effects_frame, text="☑️ 缩放变化", variable=self.scale_enabled_var)
        scale_check.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))

        scale_frame = ttk.Frame(effects_frame)
        scale_frame.grid(row=1, column=0, sticky=tk.W, padx=(120, 0), pady=(0, 5))
        ttk.Label(scale_frame, text="从:").grid(row=0, column=0, sticky=tk.W)
        self.scale_start_var = tk.StringVar(value="120")
        ttk.Spinbox(scale_frame, textvariable=self.scale_start_var, from_=50, to=200, width=5).grid(row=0, column=1, padx=(2, 5))
        ttk.Label(scale_frame, text="到:").grid(row=0, column=2, sticky=tk.W)
        self.scale_end_var = tk.StringVar(value="100")
        ttk.Spinbox(scale_frame, textvariable=self.scale_end_var, from_=50, to=200, width=5).grid(row=0, column=3, padx=(2, 2))
        ttk.Label(scale_frame, text="%").grid(row=0, column=4, sticky=tk.W)

        # 透明度变化（右侧）
        self.alpha_enabled_var = tk.BooleanVar(value=True)
        alpha_check = ttk.Checkbutton(effects_frame, text="☑️ 透明度变化", variable=self.alpha_enabled_var)
        alpha_check.grid(row=1, column=1, sticky=tk.W, pady=(0, 5))

        alpha_frame = ttk.Frame(effects_frame)
        alpha_frame.grid(row=1, column=1, sticky=tk.W, padx=(140, 0), pady=(0, 5))
        ttk.Label(alpha_frame, text="从:").grid(row=0, column=0, sticky=tk.W)
        self.alpha_start_var = tk.StringVar(value="60")
        ttk.Spinbox(alpha_frame, textvariable=self.alpha_start_var, from_=0, to=100, width=5).grid(row=0, column=1, padx=(2, 5))
        ttk.Label(alpha_frame, text="到:").grid(row=0, column=2, sticky=tk.W)
        self.alpha_end_var = tk.StringVar(value="100")
        ttk.Spinbox(alpha_frame, textvariable=self.alpha_end_var, from_=0, to=100, width=5).grid(row=0, column=3, padx=(2, 2))
        ttk.Label(alpha_frame, text="%").grid(row=0, column=4, sticky=tk.W)

        # 第3行：亮度变化 + 饱和度变化
        # 亮度变化（左侧）
        self.brightness_enabled_var = tk.BooleanVar(value=True)
        brightness_check = ttk.Checkbutton(effects_frame, text="☑️ 亮度变化", variable=self.brightness_enabled_var)
        brightness_check.grid(row=2, column=0, sticky=tk.W, pady=(0, 5))

        brightness_frame = ttk.Frame(effects_frame)
        brightness_frame.grid(row=2, column=0, sticky=tk.W, padx=(120, 0), pady=(0, 5))
        ttk.Label(brightness_frame, text="从:").grid(row=0, column=0, sticky=tk.W)
        self.brightness_start_var = tk.StringVar(value="-50")
        ttk.Spinbox(brightness_frame, textvariable=self.brightness_start_var, from_=-100, to=100, width=5).grid(row=0, column=1, padx=(2, 5))
        ttk.Label(brightness_frame, text="到:").grid(row=0, column=2, sticky=tk.W)
        self.brightness_end_var = tk.StringVar(value="0")
        ttk.Spinbox(brightness_frame, textvariable=self.brightness_end_var, from_=-100, to=100, width=5).grid(row=0, column=3, padx=(2, 0))

        # 饱和度变化（右侧）
        self.saturation_enabled_var = tk.BooleanVar(value=True)
        saturation_check = ttk.Checkbutton(effects_frame, text="☑️ 饱和度变化", variable=self.saturation_enabled_var)
        saturation_check.grid(row=2, column=1, sticky=tk.W, pady=(0, 5))

        saturation_frame = ttk.Frame(effects_frame)
        saturation_frame.grid(row=2, column=1, sticky=tk.W, padx=(140, 0), pady=(0, 5))
        ttk.Label(saturation_frame, text="从:").grid(row=0, column=0, sticky=tk.W)
        self.saturation_start_var = tk.StringVar(value="60")
        ttk.Spinbox(saturation_frame, textvariable=self.saturation_start_var, from_=0, to=300, width=5).grid(row=0, column=1, padx=(2, 5))
        ttk.Label(saturation_frame, text="到:").grid(row=0, column=2, sticky=tk.W)
        self.saturation_end_var = tk.StringVar(value="120")
        ttk.Spinbox(saturation_frame, textvariable=self.saturation_end_var, from_=0, to=300, width=5).grid(row=0, column=3, padx=(2, 2))
        ttk.Label(saturation_frame, text="%").grid(row=0, column=4, sticky=tk.W)

        # 为了保持代码兼容性，添加未使用的效果变量（设为False）
        self.particle_enabled_var = tk.BooleanVar(value=False)
        self.particle_start_var = tk.StringVar(value="0")
        self.particle_end_var = tk.StringVar(value="0")

        self.hue_enabled_var = tk.BooleanVar(value=False)
        self.hue_start_var = tk.StringVar(value="0")
        self.hue_end_var = tk.StringVar(value="0")

        self.noise_enabled_var = tk.BooleanVar(value=False)
        self.noise_start_var = tk.StringVar(value="0")
        self.noise_end_var = tk.StringVar(value="0")

        self.sharpen_enabled_var = tk.BooleanVar(value=False)
        self.sharpen_start_var = tk.StringVar(value="0")
        self.sharpen_end_var = tk.StringVar(value="0")

        # 输出设置（去掉标题）
        output_frame = ttk.Frame(main_frame, padding="5")
        output_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        output_frame.columnconfigure(1, weight=1)

        ttk.Label(output_frame, text="输出目录:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.output_dir_var = tk.StringVar(value=self.get_default_output_dir())
        output_dir_entry = ttk.Entry(output_frame, textvariable=self.output_dir_var)
        output_dir_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(output_frame, text="选择目录",
                  command=self.browse_output_dir).grid(row=0, column=2)

        # 文件名前缀和自动命名放一行
        ttk.Label(output_frame, text="前缀:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.filename_prefix_var = tk.StringVar(value="图转视频")
        prefix_entry = ttk.Entry(output_frame, textvariable=self.filename_prefix_var, width=15)
        prefix_entry.grid(row=1, column=1, sticky=tk.W, padx=(0, 10), pady=(5, 0))

        self.auto_naming_var = tk.BooleanVar(value=True)
        auto_naming_check = ttk.Checkbutton(output_frame, text="自动添加时间戳",
                                           variable=self.auto_naming_var)
        auto_naming_check.grid(row=1, column=2, sticky=tk.W, pady=(5, 0))
        
        # 进度区域（去掉标题）
        progress_frame = ttk.Frame(main_frame, padding="5")
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.StringVar(value="")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate', maximum=100)
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 移除复杂的队列界面，图片列表就是队列

        # 控制按钮 - 只要一行
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, columnspan=3, pady=(5, 0))

        self.create_button = ttk.Button(control_frame, text="开始制作视频",
                                       command=self.create_video, style="Accent.TButton")
        self.create_button.pack(side=tk.LEFT, padx=(0, 10))

        self.pause_button = ttk.Button(control_frame, text="暂停",
                                      command=self.pause_video, state="disabled")
        self.pause_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(control_frame, text="停止",
                                     command=self.stop_video, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        # 添加最小化到托盘按钮
        if TRAY_AVAILABLE:
            self.tray_button = ttk.Button(control_frame, text="最小化到托盘",
                                         command=self.tray_manager.minimize_to_tray)
            self.tray_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态栏（简化）
        self.status_var = tk.StringVar(value="")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var,
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def get_default_output_dir(self):
        """获取默认输出目录"""
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        if not os.path.exists(desktop_path):
            desktop_path = os.path.join(os.path.expanduser("~"), "桌面")
        return desktop_path

    def get_output_filename(self):
        """生成输出文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        prefix = self.filename_prefix_var.get()

        if self.auto_naming_var.get():
            return f"{prefix}_{timestamp}.mp4"
        else:
            return f"{prefix}.mp4"

    def get_full_output_path(self):
        """获取完整输出路径"""
        output_dir = self.output_dir_var.get()
        filename = self.get_output_filename()
        return os.path.join(output_dir, filename)
    


    def load_folder_images(self):
        """加载文件夹中的所有图片"""
        folder_path = filedialog.askdirectory(title="选择包含图片的文件夹")
        if folder_path:
            try:
                image_files = []
                for ext in self.processor.supported_formats:
                    pattern = os.path.join(folder_path, f"*{ext}")
                    image_files.extend(glob.glob(pattern))
                    pattern = os.path.join(folder_path, f"*{ext.upper()}")
                    image_files.extend(glob.glob(pattern))

                # 去重并按文件名排序
                image_files = list(set(image_files))  # 去重
                image_files.sort()

                if image_files:
                    self.image_paths = image_files  # 直接替换，不是添加

                    # 更新界面显示（显示完整路径）
                    self.folder_path_var.set(f"📁 {folder_path}")
                    self.image_count_var.set(f"✅ {len(image_files)} 张图片")
                    self.status_var.set("")
                else:
                    self.folder_path_var.set("❌ 文件夹中没有找到图片")
                    self.image_count_var.set("")
                    messagebox.showinfo("提示", f"文件夹中没有找到支持的图片文件\n支持格式：{', '.join(self.processor.supported_formats)}")
            except Exception as e:
                self.folder_path_var.set("❌ 加载失败")
                self.image_count_var.set("")
                messagebox.showerror("错误", f"加载文件夹失败: {e}")
    

    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择视频输出目录",
            initialdir=self.output_dir_var.get()
        )
        if directory:
            self.output_dir_var.set(directory)
    
    def update_processor_settings(self):
        """更新处理器设置"""
        try:
            # 更新分辨率 - 从显示文本中提取实际分辨率
            resolution_text = self.resolution_var.get()
            # 提取分辨率数字部分，如 "1920x1080 (1080p横屏)" -> "1920x1080"
            resolution_part = resolution_text.split(' ')[0]
            resolution = resolution_part.split('x')
            self.processor.output_width = int(resolution[0])
            self.processor.output_height = int(resolution[1])

            # 更新其他设置
            self.processor.fps = int(self.fps_var.get())
            self.processor.duration_per_image = float(self.duration_var.get())

            # 更新模糊效果设置
            self.processor.blur_enabled = self.blur_enabled_var.get()
            self.processor.blur_start = int(self.blur_start_var.get())
            self.processor.blur_end = int(self.blur_end_var.get())

            # 更新缩放效果设置
            self.processor.scale_enabled = self.scale_enabled_var.get()
            self.processor.scale_start = int(self.scale_start_var.get())
            self.processor.scale_end = int(self.scale_end_var.get())

            # 更新粒子模糊效果设置
            self.processor.particle_enabled = self.particle_enabled_var.get()
            self.processor.particle_start = int(self.particle_start_var.get())
            self.processor.particle_end = int(self.particle_end_var.get())

            # 更新亮度效果设置
            self.processor.brightness_enabled = self.brightness_enabled_var.get()
            self.processor.brightness_start = int(self.brightness_start_var.get())
            self.processor.brightness_end = int(self.brightness_end_var.get())

            # 更新饱和度效果设置
            self.processor.saturation_enabled = self.saturation_enabled_var.get()
            self.processor.saturation_start = int(self.saturation_start_var.get())
            self.processor.saturation_end = int(self.saturation_end_var.get())

            # 更新对比度效果设置
            self.processor.contrast_enabled = self.contrast_enabled_var.get()
            self.processor.contrast_start = float(self.contrast_start_var.get())
            self.processor.contrast_end = float(self.contrast_end_var.get())

            # 更新色调效果设置
            self.processor.hue_enabled = self.hue_enabled_var.get()
            self.processor.hue_start = int(self.hue_start_var.get())
            self.processor.hue_end = int(self.hue_end_var.get())

            # 更新噪点效果设置
            self.processor.noise_enabled = self.noise_enabled_var.get()
            self.processor.noise_start = int(self.noise_start_var.get())
            self.processor.noise_end = int(self.noise_end_var.get())

            # 更新锐化效果设置
            self.processor.sharpen_enabled = self.sharpen_enabled_var.get()
            self.processor.sharpen_start = float(self.sharpen_start_var.get())
            self.processor.sharpen_end = float(self.sharpen_end_var.get())

            # 更新透明度效果设置
            self.processor.alpha_enabled = self.alpha_enabled_var.get()
            self.processor.alpha_start = int(self.alpha_start_var.get())
            self.processor.alpha_end = int(self.alpha_end_var.get())

        except ValueError as e:
            raise ValueError(f"设置参数错误: {e}")
    
    def show_video_info(self):
        """显示视频信息"""
        if not self.image_paths:
            messagebox.showwarning("警告", "请先添加图片")
            return
        
        try:
            self.update_processor_settings()
            info = self.processor.get_video_info(self.image_paths)
            
            if info:
                message = f"""视频信息预览:
                
📸 图片数量: {info['total_images']} 张
⏱️ 视频时长: {info['total_duration']:.1f} 秒
🎬 帧率: {info['fps']} FPS
📐 分辨率: {info['resolution']}
💾 预估大小: {info['estimated_size_mb']:.1f} MB
🎨 视频效果: {self.effect_var.get()}"""
                
                messagebox.showinfo("视频信息", message)
        except Exception as e:
            messagebox.showerror("错误", f"获取视频信息失败: {e}")

    def process_next_image(self):
        """处理下一张图片"""
        if self.current_image_index >= len(self.image_paths):
            # 所有图片处理完成
            total_time = datetime.now() - self.queue_start_time
            total_minutes = int(total_time.total_seconds() // 60)
            total_seconds = int(total_time.total_seconds() % 60)

            messagebox.showinfo("队列完成",
                              f"🎉 队列处理完成！\n\n"
                              f"📊 处理统计：\n"
                              f"• 完成数量：{self.completed_count}/{self.total_count} 张图片\n"
                              f"• 总用时：{total_minutes}分{total_seconds}秒\n"
                              f"• 平均用时：{total_time.total_seconds()/self.total_count:.1f}秒/张")
            return

        # 获取当前图片
        current_image_path = self.image_paths[self.current_image_index]
        image_name = os.path.splitext(os.path.basename(current_image_path))[0]

        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        effect_name = self.effect_var.get()
        prefix = self.filename_prefix_var.get()

        if self.auto_naming_var.get():
            filename = f"{prefix}_{image_name}_{timestamp}.mp4"
        else:
            filename = f"{prefix}_{image_name}.mp4"

        output_path = os.path.join(self.output_dir_var.get(), filename)

        # 计算进度百分比
        progress_percent = (self.current_image_index / self.total_count) * 100

        # 计算总用时
        if self.queue_start_time:
            elapsed_time = datetime.now() - self.queue_start_time
            elapsed_minutes = int(elapsed_time.total_seconds() // 60)
            elapsed_seconds = int(elapsed_time.total_seconds() % 60)
            time_str = f"{elapsed_minutes}分{elapsed_seconds}秒"
        else:
            time_str = "0分0秒"

        # 更新详细进度显示
        self.progress_var.set(f"队列进度：{self.current_image_index + 1}/{self.total_count} ({progress_percent:.1f}%) | "
                             f"正在处理：{image_name} | 总用时：{time_str}")

        # 更新进度条
        self.progress_bar['value'] = progress_percent

        # 强制更新界面
        self.root.update()

        # 记录当前图片开始时间
        self.current_image_start_time = datetime.now()

        # 启动进度更新定时器
        self.update_progress_timer()

        # 为单张图片制作视频
        effect_code = self.effect_mapping[effect_name]
        self.start_video_processing_single_image(current_image_path, output_path, effect_code)

    def start_video_processing_single_image(self, image_path, output_path, effect_code):
        """为单张图片制作视频"""
        self.is_processing = True
        self.is_paused = False

        # 更新按钮状态
        self.create_button.config(state="disabled", text="正在处理...")
        self.pause_button.config(state="normal")
        self.stop_button.config(state="normal")
        # 队列模式不使用无限进度条
        # self.progress_bar.start()

        # 在新线程中处理视频
        self.processing_thread = threading.Thread(target=self.create_video_thread_single_image,
                                                 args=(image_path, output_path, effect_code))
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def create_video_thread_single_image(self, image_path, output_path, effect_code):
        """单张图片视频创建线程"""
        try:
            print(f"开始处理图片: {image_path}")
            print(f"输出路径: {output_path}")

            self.update_processor_settings()

            # 为单张图片制作视频
            result = self.processor.create_video_from_images(
                [image_path],  # 只处理一张图片
                output_path,
                effect_code,
                self.simple_progress_callback  # 使用简化的回调
            )

            print(f"视频处理结果: {result}")
            print(f"输出文件是否存在: {os.path.exists(output_path)}")

            # 处理完成
            self.root.after(0, lambda: self.on_single_image_complete(output_path))

        except Exception as e:
            print(f"处理错误: {e}")
            self.root.after(0, lambda: self.on_video_error(str(e)))

    def on_single_image_complete(self, output_path):
        """单张图片处理完成"""
        # 计算单张图片用时
        if self.current_image_start_time:
            single_time = datetime.now() - self.current_image_start_time
            single_seconds = single_time.total_seconds()
        else:
            single_seconds = 0

        # 更新完成计数
        self.completed_count += 1
        self.current_image_index += 1

        # 计算总进度
        progress_percent = (self.completed_count / self.total_count) * 100

        # 计算总用时
        if self.queue_start_time:
            elapsed_time = datetime.now() - self.queue_start_time
            elapsed_minutes = int(elapsed_time.total_seconds() // 60)
            elapsed_seconds = int(elapsed_time.total_seconds() % 60)
            time_str = f"{elapsed_minutes}分{elapsed_seconds}秒"
        else:
            time_str = "0分0秒"

        # 更新进度显示
        image_name = os.path.splitext(os.path.basename(output_path))[0]
        self.progress_var.set(f"✅ 已完成：{self.completed_count}/{self.total_count} ({progress_percent:.1f}%) | "
                             f"刚完成：{image_name} ({single_seconds:.1f}秒) | 总用时：{time_str}")

        # 更新进度条
        self.progress_bar['value'] = progress_percent

        if self.current_image_index < len(self.image_paths):
            # 继续处理下一张图片
            self.root.after(1000, self.process_next_image)  # 延迟1秒
        else:
            # 所有图片处理完成
            self.is_processing = False
            self.is_paused = False
            self.progress_bar.stop()

            # 重置按钮状态
            self.create_button.config(state="normal", text="开始制作视频")
            self.pause_button.config(state="disabled", text="暂停")
            self.stop_button.config(state="disabled")

            # 最终完成统计
            total_time = datetime.now() - self.queue_start_time
            total_minutes = int(total_time.total_seconds() // 60)
            total_seconds = int(total_time.total_seconds() % 60)

            self.progress_var.set(f"🎉 队列完成！{self.completed_count}/{self.total_count} (100%) | "
                                 f"总用时：{total_minutes}分{total_seconds}秒 | "
                                 f"平均：{total_time.total_seconds()/self.total_count:.1f}秒/张")

    def add_to_queue(self):
        """添加当前设置到任务队列"""
        if not self.image_paths:
            messagebox.showwarning("警告", "请先添加图片")
            return

        # 获取当前设置
        effect_display_name = self.effect_var.get()
        effect_code = self.effect_mapping[effect_display_name]
        output_path = self.get_full_output_path()

        # 创建任务
        task = {
            'images': self.image_paths.copy(),
            'effect_display': effect_display_name,
            'effect_code': effect_code,
            'output_path': output_path,
            'duration': float(self.duration_var.get()),
            'resolution': self.resolution_var.get(),
            'fps': int(self.fps_var.get())
        }

        self.task_queue.append(task)
        self.update_queue_display()
        self.status_var.set(f"已添加任务到队列，当前队列: {len(self.task_queue)} 个任务")

    def clear_queue(self):
        """清空任务队列"""
        if self.task_queue:
            result = messagebox.askyesno("确认清空", f"确定要清空队列中的 {len(self.task_queue)} 个任务吗？")
            if result:
                self.task_queue.clear()
                self.current_task_index = 0
                self.update_queue_display()
                self.status_var.set("任务队列已清空")

    def update_queue_display(self):
        """更新队列显示"""
        # 安全检查：如果queue_text组件不存在，直接返回
        if not hasattr(self, 'queue_text'):
            return

        self.queue_text.delete(1.0, tk.END)
        if not self.task_queue:
            help_text = """任务队列为空

💡 使用方法：
1. 选择图片和效果设置
2. 点击"添加到队列"按钮
3. 重复添加多个任务
4. 点击"开始队列任务"批量处理"""
            self.queue_text.insert(1.0, help_text)
            return

        for i, task in enumerate(self.task_queue):
            if i < self.current_task_index:
                status = "已完成"
            elif i == self.current_task_index and self.is_processing:
                status = "进行中"
            else:
                status = "等待中"

            filename = os.path.basename(task['output_path'])
            line = f"{i+1}. [{status}] {task['effect_display']} - {filename} ({len(task['images'])}张图片)\n"
            self.queue_text.insert(tk.END, line)

    def start_queue_processing(self):
        """开始队列任务处理"""
        if not self.task_queue:
            messagebox.showwarning("警告", "任务队列为空")
            return

        if self.is_processing:
            messagebox.showwarning("警告", "当前正在处理任务，请等待完成或停止后再开始队列任务")
            return

        self.current_task_index = 0
        self.process_next_queue_task()

    def process_next_queue_task(self):
        """处理队列中的下一个任务"""
        if self.current_task_index >= len(self.task_queue):
            # 所有任务完成
            messagebox.showinfo("完成", f"队列中的所有 {len(self.task_queue)} 个任务已完成！")
            self.current_task_index = 0
            self.update_queue_display()
            return

        # 获取当前任务
        current_task = self.task_queue[self.current_task_index]

        # 应用任务设置
        self.image_paths = current_task['images']
        self.effect_var.set(current_task['effect_display'])
        self.duration_var.set(str(current_task['duration']))
        self.resolution_var.set(current_task['resolution'])
        self.fps_var.set(str(current_task['fps']))

        # 更新显示
        self.update_image_list()
        self.update_queue_display()

    def update_image_list(self):
        """更新图片列表显示"""
        # 如果有图片列表组件，更新显示
        if hasattr(self, 'image_list_var'):
            count = len(self.image_paths)
            self.image_list_var.set(f"已选择 {count} 张图片")

        # 更新状态显示
        if self.image_paths:
            self.status_var.set(f"已选择 {len(self.image_paths)} 张图片")
        else:
            self.status_var.set("请选择图片文件夹")

        # 开始处理当前任务
        self.create_video_with_path(current_task['output_path'], current_task['effect_code'])

    def on_queue_task_complete(self, output_path):
        """队列任务完成回调"""
        self.current_task_index += 1

        if self.continuous_mode_var.get() and self.current_task_index < len(self.task_queue):
            # 连续模式，自动开始下一个任务
            self.root.after(1000, self.process_next_queue_task)  # 延迟1秒开始下一个任务
        else:
            # 单个任务完成或队列结束
            if self.current_task_index >= len(self.task_queue):
                messagebox.showinfo("完成", f"队列中的所有 {len(self.task_queue)} 个任务已完成！")
                self.current_task_index = 0
            self.update_queue_display()



    def pause_video(self):
        """暂停视频制作"""
        if self.is_processing and not self.is_paused:
            self.is_paused = True
            self.pause_button.config(text="继续", state="normal")
            self.progress_var.set("视频制作已暂停")
        elif self.is_processing and self.is_paused:
            self.is_paused = False
            self.pause_button.config(text="暂停", state="normal")
            self.progress_var.set("继续制作视频...")

    def stop_video(self):
        """停止队列处理"""
        if self.is_processing:
            result = messagebox.askyesno("确认停止", "确定要停止队列处理吗？\n当前进度将会丢失。")
            if result:
                self.is_processing = False
                self.is_paused = False

                # 重置按钮状态
                self.create_button.config(state="normal", text="开始制作视频")
                self.pause_button.config(state="disabled", text="暂停")
                self.stop_button.config(state="disabled")
                self.progress_bar.stop()
                self.progress_var.set("队列处理已停止")

    def progress_callback(self, message):
        """进度回调函数"""
        if not self.is_processing:
            return False  # 返回False表示停止处理

        # 处理暂停状态
        while self.is_paused and self.is_processing:
            self.root.update()
            self.root.after(100)  # 暂停100ms

        if not self.is_processing:
            return False

        # 在队列模式下，不覆盖队列进度信息，只在后面追加处理状态
        if hasattr(self, 'current_image_index') and hasattr(self, 'total_count') and self.total_count > 1:
            # 队列模式：保持队列进度信息，只更新处理细节
            current_progress = self.progress_var.get()
            if "队列进度：" in current_progress or "已完成：" in current_progress:
                # 如果已经有队列进度信息，就不覆盖，只在控制台输出处理细节
                print(f"处理细节: {message}")
            else:
                # 如果没有队列进度信息，才设置message
                self.progress_var.set(message)
        else:
            # 单个视频模式：正常显示
            self.progress_var.set(message)

        self.root.update_idletasks()
        return True  # 返回True表示继续处理

    def simple_progress_callback(self, message):
        """简化的进度回调函数"""
        print(f"处理进度: {message}")

        # 更新界面进度显示 - 显示具体的处理细节
        try:
            # 保持队列进度信息，只更新处理细节
            if hasattr(self, 'current_image_index') and hasattr(self, 'total_count'):
                current_progress = f"队列进度：{self.current_image_index + 1}/{self.total_count} | {message}"
                self.progress_var.set(current_progress)
            else:
                self.progress_var.set(message)
            self.root.update()  # 强制更新界面
        except:
            pass

        return True  # 始终返回True继续处理

    def update_progress_timer(self):
        """定时更新进度显示"""
        if self.is_processing and hasattr(self, 'current_image_start_time') and self.current_image_start_time:
            # 计算当前图片处理时间
            current_time = datetime.now()
            single_elapsed = current_time - self.current_image_start_time
            single_seconds = int(single_elapsed.total_seconds())

            # 计算总用时
            if self.queue_start_time:
                total_elapsed = current_time - self.queue_start_time
                total_minutes = int(total_elapsed.total_seconds() // 60)
                total_seconds = int(total_elapsed.total_seconds() % 60)
                time_str = f"{total_minutes}分{total_seconds}秒"
            else:
                time_str = "0分0秒"

            # 获取当前图片名称
            if hasattr(self, 'current_image_index') and self.image_paths:
                current_image_path = self.image_paths[self.current_image_index]
                image_name = os.path.splitext(os.path.basename(current_image_path))[0]

                # 更新进度显示
                progress_percent = ((self.current_image_index + 1) / self.total_count) * 100
                self.progress_var.set(f"队列进度：{self.current_image_index + 1}/{self.total_count} ({progress_percent:.1f}%) | "
                                     f"正在处理：{image_name} | 当前用时：{single_seconds}秒 | 总用时：{time_str}")
                self.progress_bar['value'] = progress_percent

            # 每秒更新一次
            self.root.after(1000, self.update_progress_timer)
    
    def create_video_thread(self, output_path=None, effect_code=None):
        """视频创建线程"""
        try:
            self.update_processor_settings()

            # 使用传入的参数或默认值
            if output_path is None:
                output_path = self.get_full_output_path()
            if effect_code is None:
                effect_display_name = self.effect_var.get()
                effect_code = self.effect_mapping[effect_display_name]

            # 开始处理
            self.processor.create_video_from_images(
                self.image_paths,
                output_path,
                effect_code,
                self.progress_callback
            )

            # 处理完成 - 这里不会被调用，因为使用了单独的线程方法

        except Exception as e:
            self.root.after(0, lambda: self.on_video_error(str(e)))
    
    def on_video_complete(self, output_path):
        """视频创建完成"""
        self.is_processing = False
        self.is_paused = False
        self.progress_bar.stop()

        # 重置所有按钮状态
        self.create_button.config(state="normal", text="开始制作视频")
        self.pause_button.config(state="disabled", text="暂停")
        self.stop_button.config(state="disabled")
        self.progress_var.set("视频创建完成！")

        result = messagebox.askyesno("完成",
                                   f"视频创建成功！\n文件保存在: {output_path}\n\n是否打开文件所在文件夹？")
        if result:
            try:
                os.startfile(os.path.dirname(output_path))
            except:
                pass

    def on_video_error(self, error_message):
        """视频创建错误"""
        self.is_processing = False
        self.is_paused = False
        self.progress_bar.stop()

        # 重置所有按钮状态
        self.create_button.config(state="normal", text="开始制作视频")
        self.pause_button.config(state="disabled", text="暂停")
        self.stop_button.config(state="disabled")
        self.progress_var.set("创建失败")
        messagebox.showerror("错误", f"视频创建失败: {error_message}")
    


    def create_video(self):
        """队列模式：为每张图片单独制作视频"""
        if self.is_processing:
            return

        if not self.image_paths:
            messagebox.showwarning("警告", "请先选择图片文件夹")
            return

        if not self.output_dir_var.get():
            messagebox.showwarning("警告", "请选择输出目录")
            return

        try:
            self.update_processor_settings()
        except Exception as e:
            messagebox.showerror("错误", str(e))
            return

        # 队列模式：为每张图片单独制作视频
        result = messagebox.askyesno("队列模式确认",
                                   f"将为 {len(self.image_paths)} 张图片分别制作视频\n"
                                   f"每张图片生成一个独立的视频文件\n\n"
                                   f"确定开始队列处理吗？")
        if not result:
            return

        # 初始化队列进度
        self.current_image_index = 0
        self.completed_count = 0
        self.total_count = len(self.image_paths)
        self.queue_start_time = datetime.now()

        # 开始队列处理
        self.process_next_image()

    def create_video_with_path(self, output_path, effect_code):
        """使用指定路径和效果创建视频（用于队列任务）"""
        if self.is_processing:
            return

        try:
            self.update_processor_settings()
        except Exception as e:
            messagebox.showerror("错误", str(e))
            return

        # 开始处理
        self.start_video_processing(output_path, effect_code)

    def start_video_processing(self, output_path=None, effect_code=None):
        """开始视频处理"""
        self.is_processing = True
        self.is_paused = False

        # 更新按钮状态
        self.create_button.config(state="disabled", text="正在处理...")
        self.pause_button.config(state="normal")
        self.stop_button.config(state="normal")
        self.progress_bar.start()
        self.progress_var.set("开始处理...")

        # 在新线程中处理视频
        self.processing_thread = threading.Thread(target=self.create_video_thread, args=(output_path, effect_code))
        self.processing_thread.daemon = True
        self.processing_thread.start()



def main():
    root = tk.Tk()
    app = VideoCreatorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
