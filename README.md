# 🎬 桌面图片转MP4工具

**项老师AI工作室** - 传统企业AI自动化转型专家

## 📖 项目简介

这是一个专业的桌面图片转视频工具，能够自动扫描桌面上的图片文件，并将其制作成高质量的MP4视频。支持多种视频效果和专业的过渡动画。

## ✨ 功能特色

### 🖼️ 图片处理
- **自动扫描**: 自动检测桌面上的所有图片文件
- **多格式支持**: JPG、PNG、BMP、TIFF、WebP等主流格式
- **智能缩放**: 自动调整图片尺寸，保持最佳显示效果
- **高质量处理**: 使用专业算法确保图片质量

### 🎥 视频效果（9种专业效果）
- **Ken Burns效果**: 专业的缩放和平移动画，模拟摄像机运动
- **缩放脉冲效果**: 呼吸式的缩放动画，营造节奏感
- **旋转效果**: 轻微旋转配合缩放，增加立体感
- **滑动效果**: 从不同方向滑入画面的现代动画
- **3D翻转效果**: 立体翻转动画，科技感十足
- **色彩变换效果**: 动态色彩变化，彩虹般流动
- **波浪扭曲效果**: 水波般的扭曲动画，梦幻效果
- **马赛克效果**: 从模糊到清晰的渐变显示
- **随机效果**: 每张图片随机选择不同效果
- **静态显示**: 简单的图片切换，无动画效果

### ⚙️ 技术参数
- **多分辨率**: 支持1080p、720p、4K输出
- **可调帧率**: 24/30/60 FPS选项
- **高质量编码**: MP4格式，兼容性强
- **实时预览**: 显示视频信息和预估大小

## 🚀 快速开始

### 1. 环境要求
- Python 3.7+
- Windows/macOS/Linux

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

或者运行程序时自动安装：
```bash
python main.py
```

### 3. 使用步骤
1. **准备图片**: 将要制作视频的图片放在桌面上
2. **启动程序**: 运行 `python main.py`
3. **选择设置**: 调整视频效果、分辨率、帧率等参数
4. **开始制作**: 点击"开始制作视频"按钮
5. **等待完成**: 程序会显示处理进度，完成后自动保存

## 📁 文件结构

```
桌面图片转MP4工具/
├── main.py              # 主程序入口
├── video_processor.py   # 核心视频处理模块（9种效果）
├── gui_interface.py     # 图形用户界面
├── 效果演示.py          # 视频效果演示工具
├── 启动程序.bat         # Windows一键启动脚本
├── requirements.txt     # 依赖包列表
└── README.md           # 说明文档
```

## 🔧 技术实现

### 核心技术栈
- **OpenCV**: 图像处理和视频编码
- **PIL/Pillow**: 图像格式支持和处理
- **NumPy**: 数值计算和数组操作
- **Tkinter**: 图形用户界面

### 关键算法
- **Ken Burns效果**: 通过逐帧改变缩放和位移参数实现
- **图像变换**: 使用仿射变换和透视变换
- **过渡效果**: Alpha混合算法实现淡入淡出
- **视频编码**: H.264编码，确保高质量输出

## 📊 性能特点

- **处理速度**: 优化的多线程处理，提高生成效率
- **内存管理**: 智能内存分配，支持大量图片处理
- **质量保证**: 专业的图像处理算法，确保输出质量
- **兼容性强**: 支持主流操作系统和视频播放器

## 🎯 使用场景

- **个人相册**: 将旅行照片制作成回忆视频
- **商业展示**: 产品图片制作成宣传视频
- **教育培训**: 课件图片制作成教学视频
- **社交媒体**: 快速制作短视频内容

## 🛠️ 自定义设置

### 视频参数
- **分辨率**: 1920x1080 (推荐) / 1280x720 / 3840x2160
- **帧率**: 30 FPS (推荐) / 24 FPS / 60 FPS
- **图片时长**: 3秒 (默认) / 可自定义
- **过渡时长**: 0.5秒 (默认) / 可调整

### 效果选项
- **Ken Burns**: 专业的动态缩放效果
- **静态显示**: 简单的图片切换
- **更多效果**: 后续版本将添加更多特效

## 📝 更新记录

### v1.0 (2025-01-03)
- ✅ 基础图片转视频功能
- ✅ Ken Burns动态效果
- ✅ 图形用户界面
- ✅ 多格式图片支持
- ✅ 可调节视频参数
- ✅ 自动依赖检查和安装

## 🎨 效果详细说明

### 🎬 Ken Burns效果
- **原理**: 缓慢的缩放和多方向平移
- **特点**: 模拟专业摄像机推拉镜头
- **适用**: 风景照片、人物照片

### 💓 缩放脉冲效果
- **原理**: 正弦波控制的呼吸式缩放
- **特点**: 有节奏的放大缩小
- **适用**: 突出重点内容

### 🌀 旋转效果
- **原理**: 轻微旋转配合缩放变换
- **特点**: 增加立体感和动态感
- **适用**: 艺术照片、创意内容

### 📱 滑动效果
- **原理**: 从四个方向滑入画面
- **特点**: 现代化的切换动画
- **适用**: 产品展示、介绍页面

### 🔄 3D翻转效果
- **原理**: 透视变换模拟立体翻转
- **特点**: 三维空间的视觉效果
- **适用**: 科技产品、现代设计

### 🌈 色彩变换效果
- **原理**: HSV色彩空间的动态调整
- **特点**: 彩虹般的色彩流动
- **适用**: 艺术创作、时尚内容

### 🌊 波浪扭曲效果
- **原理**: 正弦波控制的像素重映射
- **特点**: 水波般的扭曲动画
- **适用**: 创意视频、艺术表达

### 🎯 马赛克效果
- **原理**: 从低分辨率到高分辨率渐变
- **特点**: 神秘感的视觉呈现
- **适用**: 悬疑内容、揭示场景

## 🤝 技术支持

**项老师AI工作室**
- 专注于传统企业AI自动化转型
- 提供定制化AI解决方案
- 企业级技术支持服务

## 📄 许可证

本项目仅供学习和个人使用。商业使用请联系作者获得授权。

---

**© 2025 项老师AI工作室 | 传统企业AI自动化转型专家**
