#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题诊断脚本
彻底检查托盘和图标问题
"""

import os
import sys

def check_tray_environment():
    """检查托盘环境"""
    print("🔍 检查托盘功能环境...")
    
    try:
        import pystray
        print("✅ pystray 导入成功")
        
        from PIL import Image
        print("✅ PIL 导入成功")
        
        # 测试创建简单托盘
        image = Image.new('RGB', (64, 64), 'red')
        menu = pystray.Menu(pystray.MenuItem('测试', lambda: None))
        icon = pystray.Icon('测试', image, '测试', menu)
        print("✅ 托盘图标创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 托盘环境问题: {e}")
        return False

def check_icon_files():
    """检查图标文件"""
    print("\n🔍 检查图标文件...")
    
    files_to_check = ['logo.ico', 'logo.png']
    
    for filename in files_to_check:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✅ {filename} 存在 ({size} bytes)")
            
            # 检查文件内容
            try:
                from PIL import Image
                img = Image.open(filename)
                print(f"   📊 格式: {img.format}, 大小: {img.size}, 模式: {img.mode}")
            except Exception as e:
                print(f"   ❌ 文件损坏: {e}")
        else:
            print(f"❌ {filename} 不存在")

def check_source_code():
    """检查源代码中的关键问题"""
    print("\n🔍 检查源代码...")
    
    # 检查tray_manager.py
    if os.path.exists('tray_manager.py'):
        print("✅ tray_manager.py 存在")
        
        with open('tray_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键方法
        methods = ['minimize_to_tray', 'show_window', 'quit_application', 'on_window_close']
        for method in methods:
            if f"def {method}" in content:
                print(f"   ✅ {method} 方法存在")
            else:
                print(f"   ❌ {method} 方法缺失")
                
        # 检查pystray导入
        if "import pystray" in content:
            print("   ✅ pystray 正确导入")
        else:
            print("   ❌ pystray 导入缺失")
    else:
        print("❌ tray_manager.py 不存在")
    
    # 检查gui_interface.py
    if os.path.exists('gui_interface.py'):
        print("✅ gui_interface.py 存在")
        
        with open('gui_interface.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键集成
        checks = [
            ("from tray_manager import TrayManager", "TrayManager导入"),
            ("self.tray_manager = TrayManager", "TrayManager初始化"),
            ("self.tray_manager.on_window_close", "关闭事件绑定"),
            ("self.tray_manager.minimize_to_tray", "托盘按钮绑定")
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} 缺失")
    else:
        print("❌ gui_interface.py 不存在")

def check_executable():
    """检查可执行文件"""
    print("\n🔍 检查可执行文件...")
    
    desktop = os.path.expanduser("~/Desktop")
    exe_files = []
    
    try:
        for file in os.listdir(desktop):
            if file.endswith('.exe') and '批量图片' in file:
                exe_files.append(file)
        
        if exe_files:
            print(f"✅ 找到 {len(exe_files)} 个可执行文件:")
            for exe in exe_files:
                full_path = os.path.join(desktop, exe)
                size = os.path.getsize(full_path) / (1024*1024)
                print(f"   📁 {exe} ({size:.1f} MB)")
        else:
            print("❌ 没有找到可执行文件")
            
    except Exception as e:
        print(f"❌ 检查可执行文件失败: {e}")

def run_source_test():
    """运行源代码测试"""
    print("\n🔍 测试源代码运行...")
    
    try:
        # 测试导入
        from gui_interface import VideoConverterGUI
        print("✅ GUI类导入成功")
        
        # 测试托盘管理器
        from tray_manager import TrayManager
        print("✅ TrayManager导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 源代码测试失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("=" * 60)
    print("🔍 批量图片自动转短视频 - 问题诊断")
    print("=" * 60)
    
    # 执行所有检查
    tray_ok = check_tray_environment()
    check_icon_files()
    check_source_code()
    check_executable()
    source_ok = run_source_test()
    
    print("\n" + "=" * 60)
    print("📊 诊断结果汇总:")
    print("=" * 60)
    
    print(f"托盘环境: {'✅ 正常' if tray_ok else '❌ 异常'}")
    print(f"源代码: {'✅ 正常' if source_ok else '❌ 异常'}")
    
    if not tray_ok:
        print("\n🔧 托盘问题可能原因:")
        print("- pystray依赖包损坏或版本不兼容")
        print("- PIL图像处理库问题")
        print("- Windows系统托盘权限问题")
    
    if not source_ok:
        print("\n🔧 源代码问题可能原因:")
        print("- 模块导入路径错误")
        print("- 类定义或方法缺失")
        print("- 语法错误或依赖缺失")
    
    print("\n" + "=" * 60)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
