#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量图片自动转短视频 - 独立可执行程序构建脚本
项老师AI工作室出品
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def install_pyinstaller():
    """安装PyInstaller"""
    print("🔧 检查PyInstaller...")
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
            return True
        except Exception as e:
            print(f"❌ PyInstaller安装失败: {e}")
            return False

def build_executable():
    """构建独立可执行程序"""
    print("\n🚀 开始构建独立可执行程序...")
    
    # 程序信息
    app_name = "批量图片自动转短视频"
    version = "v2.6"
    author = "项老师AI工作室"
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 无控制台窗口
        "--name", f"{app_name}_{version}",  # 可执行文件名
        "--icon", "logo.ico",           # 使用项老师的logo作为图标
        "--add-data", "logo.ico;.",     # 包含图标文件
        "--add-data", "logo.png;.",     # 包含PNG图标
        "--hidden-import", "PIL",       # 包含PIL库
        "--hidden-import", "cv2",       # 包含OpenCV
        "--hidden-import", "numpy",     # 包含NumPy
        "--hidden-import", "tkinter",   # 包含Tkinter
        "--clean",                      # 清理临时文件
        "main.py"                       # 主程序文件
    ]
    
    print(f"📋 构建命令: {' '.join(cmd)}")
    
    try:
        # 执行构建
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            
            # 检查生成的文件
            exe_path = f"dist/{app_name}_{version}.exe"
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 可执行文件: {exe_path}")
                print(f"📊 文件大小: {file_size:.1f} MB")
                
                # 复制到桌面
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                if os.path.exists(desktop_path):
                    desktop_exe = os.path.join(desktop_path, f"{app_name}_{version}.exe")
                    try:
                        shutil.copy2(exe_path, desktop_exe)
                        print(f"✅ 已复制到桌面: {desktop_exe}")
                    except Exception as e:
                        print(f"⚠️ 复制到桌面失败: {e}")
                
                return True
            else:
                print("❌ 未找到生成的可执行文件")
                return False
        else:
            print("❌ 构建失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_build_info():
    """创建构建信息文件"""
    build_info = f"""# 批量图片自动转短视频 - 构建信息

## 程序信息
- **名称**: 批量图片自动转短视频
- **版本**: v2.6
- **作者**: 项老师AI工作室
- **构建时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 功能特色
- 🎬 批量处理图片文件
- 📱 支持多种图片格式 (JPG, PNG, BMP, TIFF, WebP)
- ✨ 10重线性变化效果 (模糊、缩放、粒子、亮度、饱和度、对比度、色调、噪点、锐化、透明度)
- 🎯 15种专业动态效果
- ⚙️ 可调节视频参数 (分辨率、帧率、时长)
- 🎥 高质量短视频输出
- 📊 实时队列进度显示
- 🔄 队列模式：一张图片生成一个视频

## 使用说明
1. 双击运行可执行文件
2. 选择包含图片的文件夹
3. 设置视频效果和参数
4. 选择输出目录
5. 点击"开始制作视频"按钮
6. 等待批量自动生成短视频文件

## 技术规格
- **开发语言**: Python 3.8+
- **图形界面**: Tkinter
- **图像处理**: OpenCV + PIL
- **视频编码**: MP4V
- **支持系统**: Windows 10/11

## 版权信息
© 2025 项老师AI工作室 | 传统企业AI自动化转型专家

---
**项老师AI工作室** - 定制超级总裁助理 AI应用培训 企业AI自动化转型
"""
    
    with open("构建信息.md", "w", encoding="utf-8") as f:
        f.write(build_info)
    
    print("📝 构建信息文件已创建: 构建信息.md")

def main():
    """主函数"""
    print("=" * 60)
    print("🎬 批量图片自动转短视频 - 独立可执行程序构建器")
    print("📱 项老师AI工作室出品")
    print("=" * 60)
    
    # 检查必要文件
    required_files = ["main.py", "gui_interface.py", "video_processor.py", "logo.ico"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件已就绪")
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return False
    
    # 构建可执行程序
    if build_executable():
        create_build_info()
        print("\n🎉 构建完成！")
        print("📁 可执行文件已生成并复制到桌面")
        print("🚀 您现在可以双击运行独立的可执行程序了！")
        return True
    else:
        print("\n❌ 构建失败！")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
